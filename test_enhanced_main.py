#!/usr/bin/env python3
"""
Test script for the enhanced MCP implementation

This script validates the enhanced features and ensures everything works correctly.
"""

import asyncio
import json
import os
import sys
from typing import Dict, Any

# Import the enhanced classes
from main import (
    EnhancedMCPClientManager,
    EnhancedMCPServerConnection,
    MCPServerConfig,
    telemetry
)

async def test_enhanced_features():
    """Test the enhanced MCP features"""
    print("🧪 Testing Enhanced MCP Implementation")
    print("=" * 50)
    
    # Test 1: Enhanced Client Manager Creation
    print("\n1. Testing Enhanced Client Manager Creation...")
    try:
        manager = EnhancedMCPClientManager()
        print("✅ Enhanced client manager created successfully")
        print(f"   - Connection pool stats: {manager.connection_pool_stats}")
        print(f"   - Global registries initialized: {len(manager.global_tool_registry)} tools, {len(manager.global_resource_registry)} resources")
    except Exception as e:
        print(f"❌ Failed to create enhanced client manager: {e}")
        return False
    
    # Test 2: Enhanced Server Configuration
    print("\n2. Testing Enhanced Server Configuration...")
    try:
        config = MCPServerConfig(
            name="test-server",
            command="echo",
            args=["test"],
            transport_type="stdio",
            connection_timeout=30.0,
            max_retries=3,
            enabled=True
        )
        print("✅ Enhanced server configuration created successfully")
        print(f"   - Server: {config.name}")
        print(f"   - Transport: {config.transport_type}")
        print(f"   - Timeout: {config.connection_timeout}s")
    except Exception as e:
        print(f"❌ Failed to create server configuration: {e}")
        return False
    
    # Test 3: Enhanced Connection Class
    print("\n3. Testing Enhanced Connection Class...")
    try:
        connection = EnhancedMCPServerConnection(config)
        print("✅ Enhanced connection created successfully")
        print(f"   - Status: {connection.status}")
        print(f"   - Transport type: {connection.config.transport_type}")
        print(f"   - Connection metrics initialized: {bool(connection.connection_metrics)}")
        print(f"   - Progress tokens: {len(connection.active_progress_tokens)}")
    except Exception as e:
        print(f"❌ Failed to create enhanced connection: {e}")
        return False
    
    # Test 4: Telemetry System
    print("\n4. Testing Enhanced Telemetry System...")
    try:
        # Record some test metrics
        telemetry.record_tool_execution(100.5, "test-server", "test-tool")
        telemetry.record_error("test_error")
        telemetry.record_progress_event()
        
        summary = telemetry.get_summary()
        print("✅ Telemetry system working correctly")
        print(f"   - Total requests: {summary['total_requests']}")
        print(f"   - Error types tracked: {len(summary['error_types'])}")
        print(f"   - Progress events: {summary['progress_events']}")
    except Exception as e:
        print(f"❌ Telemetry system error: {e}")
        return False
    
    # Test 5: Enhanced Validation
    print("\n5. Testing Enhanced Validation...")
    try:
        # Test validation with a mock tool
        connection.tool_registry["test_tool"] = {
            "description": "Test tool",
            "input_schema": {
                "type": "object",
                "properties": {
                    "message": {"type": "string"},
                    "count": {"type": "integer", "minimum": 1}
                },
                "required": ["message"]
            }
        }
        
        # Test valid arguments
        valid_args = {"message": "hello", "count": 5}
        validated_args, warnings, is_valid = connection.validate_tool_arguments_enhanced("test_tool", valid_args)
        
        if is_valid and not warnings:
            print("✅ Enhanced validation working correctly")
            print(f"   - Valid arguments processed: {validated_args}")
        else:
            print(f"⚠️  Validation warnings: {warnings}")
            
        # Test invalid arguments
        invalid_args = {"count": 5}  # Missing required 'message'
        validated_args, warnings, is_valid = connection.validate_tool_arguments_enhanced("test_tool", invalid_args)
        
        if not is_valid and warnings:
            print("✅ Enhanced validation correctly caught invalid arguments")
            print(f"   - Validation warnings: {warnings}")
        else:
            print("⚠️  Validation should have caught missing required field")
            
    except Exception as e:
        print(f"❌ Enhanced validation error: {e}")
        return False
    
    # Test 6: Registry Management
    print("\n6. Testing Enhanced Registry Management...")
    try:
        # Add test data to registries
        manager.global_tool_registry["test::test_tool"] = {
            "server": "test-server",
            "tool_name": "test_tool",
            "description": "Test tool",
            "input_schema": {},
            "metadata": {"registered_at": 1234567890}
        }
        
        manager.global_resource_registry["test-server:test-resource"] = {
            "server": "test-server",
            "uri": "test-resource",
            "name": "Test Resource",
            "description": "Test resource",
            "mime_type": "text/plain",
            "metadata": {"registered_at": 1234567890}
        }
        
        # Test registry access
        tools = manager.get_available_tools()
        resources = manager.get_available_resources()
        
        print("✅ Enhanced registry management working correctly")
        print(f"   - Tools in registry: {len(tools)}")
        print(f"   - Resources in registry: {len(resources)}")
        
    except Exception as e:
        print(f"❌ Registry management error: {e}")
        return False
    
    # Test 7: Connection Pool Status
    print("\n7. Testing Connection Pool Status...")
    try:
        status = await manager.get_connection_pool_status()
        print("✅ Connection pool status working correctly")
        print(f"   - Pool stats: {status['pool_stats']}")
        print(f"   - Global registries: {status['global_registries']}")
        print(f"   - Active sessions: {status['active_sessions']}")
        
    except Exception as e:
        print(f"❌ Connection pool status error: {e}")
        return False
    
    # Test 8: Cleanup
    print("\n8. Testing Enhanced Cleanup...")
    try:
        cleanup_result = await manager.cleanup()
        print("✅ Enhanced cleanup working correctly")
        print(f"   - Cleanup time: {cleanup_result['cleanup_time']:.2f}ms")
        print(f"   - Successful cleanups: {cleanup_result['successful_cleanups']}")
        
    except Exception as e:
        print(f"❌ Enhanced cleanup error: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All Enhanced MCP Features Tested Successfully!")
    print("\nKey Features Validated:")
    print("✅ Enhanced Client Manager with connection pooling")
    print("✅ Enhanced Server Connection with progress tracking")
    print("✅ Comprehensive telemetry and observability")
    print("✅ Advanced validation with JSON Schema support")
    print("✅ Global registry management with metadata")
    print("✅ Connection pool monitoring")
    print("✅ Enhanced cleanup with detailed reporting")
    
    return True

async def test_backward_compatibility():
    """Test backward compatibility with existing code"""
    print("\n🔄 Testing Backward Compatibility")
    print("=" * 50)
    
    try:
        # Import using old names (should work due to aliases)
        from main import MCPClientManager, MCPServerConnection
        
        # Create instances using old class names
        old_manager = MCPClientManager()
        
        print("✅ Backward compatibility maintained")
        print(f"   - MCPClientManager alias works: {type(old_manager).__name__}")
        print("   - Existing code should continue to work without changes")
        
        return True
        
    except Exception as e:
        print(f"❌ Backward compatibility error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Enhanced MCP Implementation Test Suite")
    print("Testing the completely rewritten main.py with latest MCP SDK features")
    
    try:
        # Run enhanced features test
        enhanced_success = asyncio.run(test_enhanced_features())
        
        # Run backward compatibility test
        compat_success = asyncio.run(test_backward_compatibility())
        
        if enhanced_success and compat_success:
            print("\n🎊 ALL TESTS PASSED!")
            print("\nYour enhanced MCP implementation is ready to use!")
            print("\nNext steps:")
            print("1. Install dependencies: pip install -r requirements.txt")
            print("2. Start the enhanced server: python main.py")
            print("3. Test with real MCP servers: python test_mcp_implementation.py")
            return True
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            return False
            
    except Exception as e:
        print(f"\n💥 Test suite error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
