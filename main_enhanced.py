"""
Enhanced MCP Client API with Phase 1 & 2 Improvements
- SDK-based connection management with automatic reconnection
- Enhanced tool execution with better error handling and recovery
- Comprehensive resource management capabilities
- Improved schema validation with MCP patterns
"""

import asyncio
import logging
import os
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# Import enhanced components
from main import MCPClientManager, DEFAULT_MCP_SERVERS
from enhanced_mcp_manager import EnhancedMCPMixin

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedMCPClientManagerV2(MCPClientManager, EnhancedMCPMixin):
    """
    Enhanced MCP Client Manager V2 with all Phase 1 & 2 improvements:
    - SDK-based connection management
    - Enhanced tool execution with recovery
    - Comprehensive resource management
    - Improved schema validation
    """
    
    def __init__(self):
        super().__init__()
        self.model_id = os.getenv("BEDROCK_MODEL_ID", "apac.amazon.nova-lite-v1:0")
        self._async_bedrock_client = None
        logger.info("Enhanced MCP Client Manager V2 initialized with all improvements")

    async def get_server_health_status(self) -> Dict[str, Dict]:
        """Get comprehensive health status of all servers"""
        health_status = {}
        
        for server_name, connection in self.connections.items():
            try:
                # Use enhanced connection health check
                is_healthy = await connection.ensure_connected()
                
                health_status[server_name] = {
                    "status": connection.status,
                    "healthy": is_healthy,
                    "error": connection.error,
                    "tools_count": len(connection.tools),
                    "resources_count": len(connection.resources),
                    "last_heartbeat": connection.last_heartbeat,
                    "reconnect_attempts": connection.reconnect_attempts,
                    "max_reconnect_attempts": connection.max_reconnect_attempts
                }
            except Exception as e:
                health_status[server_name] = {
                    "status": "error",
                    "healthy": False,
                    "error": str(e),
                    "tools_count": 0,
                    "resources_count": 0
                }
        
        return health_status

    async def get_comprehensive_capabilities(self) -> Dict[str, Any]:
        """Get comprehensive capabilities from all servers"""
        capabilities = {
            "servers": {},
            "global_summary": {
                "total_tools": len(self.global_tool_registry),
                "total_resources": len(self.global_resource_registry),
                "connected_servers": 0,
                "healthy_servers": 0
            }
        }
        
        for server_name in self.connections.keys():
            server_capabilities = await self.discover_server_capabilities_enhanced(server_name)
            capabilities["servers"][server_name] = server_capabilities
            
            if server_capabilities.get("status") == "connected":
                capabilities["global_summary"]["connected_servers"] += 1
                
            # Check if server is healthy (no error in capabilities)
            if "error" not in server_capabilities:
                capabilities["global_summary"]["healthy_servers"] += 1
        
        return capabilities

# Global enhanced manager instance
enhanced_mcp_manager_v2 = EnhancedMCPClientManagerV2()

# Request/Response models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    use_tools: bool = True

class ChatResponse(BaseModel):
    response: str
    session_id: str
    tools_used: List[Dict[str, Any]] = []

class ResourceRequest(BaseModel):
    resource_uri: str
    server_name: Optional[str] = None
    session_id: Optional[str] = None

class ResourceResponse(BaseModel):
    success: bool
    resource_uri: str
    content: Optional[Any] = None
    server: Optional[str] = None
    metadata: Optional[Dict] = None
    error: Optional[str] = None

class HealthResponse(BaseModel):
    servers: Dict[str, Dict]
    global_summary: Dict[str, Any]

class CapabilitiesResponse(BaseModel):
    servers: Dict[str, Any]
    global_summary: Dict[str, Any]

# Enhanced lifespan management
@asynccontextmanager
async def enhanced_lifespan_v2(app: FastAPI):
    """Enhanced application lifespan with comprehensive server management"""
    logger.info("Starting Enhanced MCP Client API V2")
    
    # Initialize servers with enhanced connection management
    config_task = None
    monitor_task = None
    
    try:
        # Load and connect to default servers
        for server_config in DEFAULT_MCP_SERVERS:
            if server_config.enabled:
                logger.info(f"Adding enhanced server: {server_config.name}")
                success = await enhanced_mcp_manager_v2.add_server(server_config)
                if success:
                    logger.info(f"✅ Enhanced server {server_config.name} connected successfully")
                else:
                    logger.warning(f"⚠️ Enhanced server {server_config.name} failed to connect")
        
        # Start enhanced monitoring task
        async def enhanced_monitor_connections():
            """Enhanced connection monitoring with health checks"""
            while True:
                try:
                    await asyncio.sleep(30)  # Check every 30 seconds
                    health_status = await enhanced_mcp_manager_v2.get_server_health_status()
                    
                    unhealthy_servers = [
                        name for name, status in health_status.items() 
                        if not status.get("healthy", False)
                    ]
                    
                    if unhealthy_servers:
                        logger.warning(f"Unhealthy servers detected: {unhealthy_servers}")
                    else:
                        logger.debug("All servers healthy")
                        
                except Exception as e:
                    logger.error(f"Enhanced monitoring error: {e}")
        
        monitor_task = asyncio.create_task(enhanced_monitor_connections())
        logger.info("Enhanced MCP Client API V2 startup complete")
        
        yield
        
    finally:
        logger.info("Shutting down Enhanced MCP Client API V2")
        
        # Cancel monitoring task
        if monitor_task and not monitor_task.done():
            monitor_task.cancel()
            try:
                await monitor_task
            except asyncio.CancelledError:
                pass
        
        # Enhanced cleanup
        try:
            await enhanced_mcp_manager_v2.cleanup()
        except Exception as e:
            logger.error(f"Enhanced cleanup error: {e}")

# Create FastAPI app with enhanced features
app = FastAPI(
    title="Enhanced MCP Client API V2 with SDK Patterns",
    description="Multi-server MCP client with enhanced connection management, tool execution, resource management, and schema validation",
    version="2.0.0",
    lifespan=enhanced_lifespan_v2
)

@app.get("/")
async def root():
    """Root endpoint with enhanced information"""
    return {
        "message": "Enhanced MCP Client API V2 with SDK Patterns",
        "version": "2.0.0",
        "features": [
            "SDK-based connection management",
            "Enhanced tool execution with recovery",
            "Comprehensive resource management",
            "Improved schema validation",
            "Automatic reconnection",
            "Health monitoring"
        ]
    }

@app.post("/chat", response_model=ChatResponse)
async def enhanced_chat_endpoint(request: ChatRequest):
    """Enhanced chat endpoint with improved error handling"""
    try:
        # Generate session ID if not provided
        session_id = request.session_id or f"enhanced_conv_{asyncio.get_event_loop().time()}"
        
        # Get available tools
        available_tools = enhanced_mcp_manager_v2.get_available_tools()
        tools_available = list(available_tools.keys()) if request.use_tools else []
        
        # Use enhanced chat with context
        result = await enhanced_mcp_manager_v2.chat_with_bedrock_with_context(
            request.message,
            session_id,
            tools_available
        )
        
        return ChatResponse(
            response=result["response"],
            session_id=result["session_id"],
            tools_used=result.get("tools_used", [])
        )
        
    except Exception as e:
        logger.error(f"Enhanced chat endpoint error: {e}")
        raise HTTPException(status_code=500, detail=f"Enhanced chat failed: {str(e)}")

@app.post("/resource/read", response_model=ResourceResponse)
async def read_resource_endpoint(request: ResourceRequest):
    """Enhanced resource reading endpoint"""
    try:
        if request.session_id:
            # Use enhanced resource reading with session context
            result = await enhanced_mcp_manager_v2.get_resource_content_with_context(
                request.resource_uri,
                request.session_id,
                request.server_name
            )
            
            return ResourceResponse(
                success=result["success"],
                resource_uri=request.resource_uri,
                content=result.get("content"),
                server=result.get("server"),
                metadata=result.get("metadata"),
                error=result.get("error")
            )
        else:
            # Use standard enhanced resource reading
            result = await enhanced_mcp_manager_v2.read_resource_enhanced(
                request.resource_uri,
                request.server_name
            )
            
            if result:
                return ResourceResponse(
                    success=True,
                    resource_uri=request.resource_uri,
                    content=result["content"],
                    server=result["server"],
                    metadata=result.get("metadata")
                )
            else:
                return ResourceResponse(
                    success=False,
                    resource_uri=request.resource_uri,
                    error="Resource not found or inaccessible"
                )
                
    except Exception as e:
        logger.error(f"Enhanced resource reading error: {e}")
        return ResourceResponse(
            success=False,
            resource_uri=request.resource_uri,
            error=f"Resource reading failed: {str(e)}"
        )

@app.get("/health", response_model=HealthResponse)
async def health_check_endpoint():
    """Enhanced health check endpoint"""
    try:
        health_status = await enhanced_mcp_manager_v2.get_server_health_status()
        
        # Calculate global summary
        total_servers = len(health_status)
        healthy_servers = sum(1 for status in health_status.values() if status.get("healthy", False))
        connected_servers = sum(1 for status in health_status.values() if status.get("status") == "connected")
        
        global_summary = {
            "total_servers": total_servers,
            "healthy_servers": healthy_servers,
            "connected_servers": connected_servers,
            "health_percentage": (healthy_servers / total_servers * 100) if total_servers > 0 else 0
        }
        
        return HealthResponse(
            servers=health_status,
            global_summary=global_summary
        )
        
    except Exception as e:
        logger.error(f"Enhanced health check error: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@app.get("/capabilities", response_model=CapabilitiesResponse)
async def capabilities_endpoint():
    """Enhanced capabilities discovery endpoint"""
    try:
        capabilities = await enhanced_mcp_manager_v2.get_comprehensive_capabilities()
        
        return CapabilitiesResponse(
            servers=capabilities["servers"],
            global_summary=capabilities["global_summary"]
        )
        
    except Exception as e:
        logger.error(f"Enhanced capabilities discovery error: {e}")
        raise HTTPException(status_code=500, detail=f"Capabilities discovery failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
