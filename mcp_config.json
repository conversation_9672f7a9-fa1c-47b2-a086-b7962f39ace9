{"mcpServers": {"bedrock-chat-server": {"command": "python", "args": ["fastmcp_server.py"], "env": {"AWS_REGION": "us-east-1", "BEDROCK_MODEL_ID": "apac.amazon.nova-lite-v1:0"}, "description": "AWS Bedrock Chat Server with MCP integration", "capabilities": ["tools", "resources", "logging"]}, "bedrock-chat-server-lowlevel": {"command": "python", "args": ["mcp_server.py"], "env": {"AWS_REGION": "us-east-1", "BEDROCK_MODEL_ID": "apac.amazon.nova-lite-v1:0"}, "description": "Low-level AWS Bedrock Chat Server implementation", "capabilities": ["tools", "resources"]}, "aws-cloudformation": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-cloudformation"], "env": {"AWS_REGION": "us-east-1"}, "description": "AWS CloudFormation MCP Server", "capabilities": ["tools", "resources"]}, "aws-cost-explorer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-aws-cost-explorer"], "env": {"AWS_REGION": "us-east-1"}, "description": "AWS Cost Explorer MCP Server", "capabilities": ["tools"]}, "aws-pricing": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-aws-pricing"], "env": {"AWS_REGION": "us-east-1"}, "description": "AWS Pricing MCP Server", "capabilities": ["tools"]}}, "clientConfig": {"defaultTimeout": 30000, "maxRetries": 3, "retryDelay": 1000, "heartbeatInterval": 60000, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}}, "serverConfig": {"fastmcp": {"host": "localhost", "port": 8000, "debug": true, "auto_reload": true}, "lowlevel": {"stdio": true, "buffer_size": 8192}}, "features": {"structured_output": true, "progress_tracking": true, "resource_subscriptions": false, "authentication": false, "batch_operations": true}, "deployment": {"environment": "development", "docker": {"enabled": false, "image": "bedrock-mcp-server", "tag": "latest"}, "monitoring": {"enabled": true, "metrics_endpoint": "/metrics", "health_endpoint": "/health"}}}