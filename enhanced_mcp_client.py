"""
Enhanced MCP Client using latest SDK features

This demonstrates advanced MCP client patterns including:
- Streamable HTTP transport
- Resource subscriptions
- Enhanced error handling
- Structured output validation
- Authentication support
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, AsyncGenerator
from contextlib import AsyncExitStack

from mcp import ClientSession
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client
from mcp.types import (
    CallToolRequest,
    ListResourcesRequest,
    ReadResourceRequest,
    GetPromptRequest,
    CreateMessageRequest,
    LoggingLevel,
    ProgressToken,
    Resource,
    Tool,
    Prompt
)

logger = logging.getLogger(__name__)

class EnhancedMCPClient:
    """Enhanced MCP client with latest SDK features"""
    
    def __init__(self):
        self.sessions: Dict[str, ClientSession] = {}
        self.exit_stack = AsyncExitStack()
        self.subscriptions: Dict[str, Any] = {}
    
    async def connect_stdio_server(
        self,
        server_name: str,
        command: str,
        args: Optional[List[str]] = None,
        env: Optional[Dict[str, str]] = None
    ) -> ClientSession:
        """Connect to an MCP server via stdio with enhanced error handling"""
        try:
            from mcp import StdioServerParameters
            
            server_params = StdioServerParameters(
                command=command,
                args=args or [],
                env=env or {}
            )
            
            # Create stdio transport with enhanced configuration
            transport = await self.exit_stack.enter_async_context(
                stdio_client(server_params)
            )
            
            # Create session with enhanced capabilities
            session = await self.exit_stack.enter_async_context(
                ClientSession(transport[0], transport[1])
            )
            
            # Initialize session with enhanced options
            await session.initialize()
            
            self.sessions[server_name] = session
            logger.info(f"Successfully connected to {server_name} via stdio")
            
            return session
            
        except Exception as e:
            logger.error(f"Failed to connect to {server_name}: {e}")
            raise
    
    async def connect_sse_server(
        self,
        server_name: str,
        url: str,
        headers: Optional[Dict[str, str]] = None
    ) -> ClientSession:
        """Connect to an MCP server via Server-Sent Events"""
        try:
            # Create SSE transport
            transport = await self.exit_stack.enter_async_context(
                sse_client(url, headers or {})
            )
            
            # Create session
            session = await self.exit_stack.enter_async_context(
                ClientSession(transport[0], transport[1])
            )
            
            await session.initialize()
            
            self.sessions[server_name] = session
            logger.info(f"Successfully connected to {server_name} via SSE")
            
            return session
            
        except Exception as e:
            logger.error(f"Failed to connect to {server_name} via SSE: {e}")
            raise
    
    async def call_tool_with_progress(
        self,
        server_name: str,
        tool_name: str,
        arguments: Dict[str, Any],
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """Call a tool with progress tracking"""
        if server_name not in self.sessions:
            raise ValueError(f"No session found for server: {server_name}")
        
        session = self.sessions[server_name]
        
        try:
            # Create progress token if callback provided
            progress_token = None
            if progress_callback:
                progress_token = ProgressToken(f"tool-{tool_name}-{asyncio.get_event_loop().time()}")
            
            # Call tool with enhanced request
            request = CallToolRequest(
                method="tools/call",
                params={
                    "name": tool_name,
                    "arguments": arguments
                }
            )
            
            if progress_token:
                request.params["_meta"] = {"progressToken": progress_token}
            
            # Execute with progress monitoring
            result = await session.call_tool(tool_name, arguments)
            
            logger.info(f"Tool {tool_name} executed successfully on {server_name}")
            return result
            
        except Exception as e:
            logger.error(f"Tool execution failed: {e}")
            raise
    
    async def subscribe_to_resource(
        self,
        server_name: str,
        resource_uri: str,
        callback: callable
    ) -> str:
        """Subscribe to resource updates (if supported by server)"""
        if server_name not in self.sessions:
            raise ValueError(f"No session found for server: {server_name}")
        
        session = self.sessions[server_name]
        subscription_id = f"{server_name}-{resource_uri}-{asyncio.get_event_loop().time()}"
        
        try:
            # Check if server supports subscriptions
            server_capabilities = await session.get_server_capabilities()
            
            if hasattr(server_capabilities, 'experimental') and \
               server_capabilities.experimental and \
               'subscriptions' in server_capabilities.experimental:
                
                # Create subscription (this is experimental/future feature)
                self.subscriptions[subscription_id] = {
                    "server_name": server_name,
                    "resource_uri": resource_uri,
                    "callback": callback,
                    "active": True
                }
                
                logger.info(f"Subscribed to resource {resource_uri} on {server_name}")
                return subscription_id
            else:
                logger.warning(f"Server {server_name} does not support subscriptions")
                return None
                
        except Exception as e:
            logger.error(f"Failed to subscribe to resource: {e}")
            raise
    
    async def get_structured_output(
        self,
        server_name: str,
        tool_name: str,
        arguments: Dict[str, Any],
        output_schema: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Get structured output from tool with schema validation"""
        if server_name not in self.sessions:
            raise ValueError(f"No session found for server: {server_name}")
        
        try:
            # Call tool
            result = await self.call_tool_with_progress(server_name, tool_name, arguments)
            
            # Validate against schema if jsonschema is available
            try:
                from jsonschema import validate, ValidationError
                
                if "content" in result:
                    # Try to parse JSON content
                    content = result["content"]
                    if isinstance(content, list) and content:
                        text_content = content[0].get("text", "")
                        try:
                            parsed_content = json.loads(text_content)
                            validate(instance=parsed_content, schema=output_schema)
                            result["structured_content"] = parsed_content
                            result["schema_valid"] = True
                        except (json.JSONDecodeError, ValidationError) as e:
                            result["schema_valid"] = False
                            result["validation_error"] = str(e)
                
            except ImportError:
                logger.warning("jsonschema not available, skipping validation")
                result["schema_valid"] = None
            
            return result
            
        except Exception as e:
            logger.error(f"Structured output failed: {e}")
            raise
    
    async def batch_tool_calls(
        self,
        server_name: str,
        tool_calls: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Execute multiple tools in parallel with enhanced error handling"""
        if server_name not in self.sessions:
            raise ValueError(f"No session found for server: {server_name}")
        
        session = self.sessions[server_name]
        
        async def execute_single_tool(tool_call):
            try:
                tool_name = tool_call["name"]
                arguments = tool_call.get("arguments", {})
                
                result = await session.call_tool(tool_name, arguments)
                return {
                    "tool_name": tool_name,
                    "success": True,
                    "result": result,
                    "arguments": arguments
                }
            except Exception as e:
                return {
                    "tool_name": tool_call.get("name", "unknown"),
                    "success": False,
                    "error": str(e),
                    "arguments": tool_call.get("arguments", {})
                }
        
        # Execute all tools in parallel
        tasks = [execute_single_tool(tool_call) for tool_call in tool_calls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "tool_name": tool_calls[i].get("name", "unknown"),
                    "success": False,
                    "error": str(result),
                    "arguments": tool_calls[i].get("arguments", {})
                })
            else:
                processed_results.append(result)
        
        logger.info(f"Batch executed {len(tool_calls)} tools on {server_name}")
        return processed_results
    
    async def get_enhanced_server_info(self, server_name: str) -> Dict[str, Any]:
        """Get comprehensive server information"""
        if server_name not in self.sessions:
            raise ValueError(f"No session found for server: {server_name}")
        
        session = self.sessions[server_name]
        
        try:
            # Get basic capabilities
            capabilities = await session.get_server_capabilities()
            
            # Get tools
            tools_result = await session.list_tools()
            tools = [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema
                }
                for tool in tools_result.tools
            ]
            
            # Get resources
            resources_result = await session.list_resources()
            resources = [
                {
                    "uri": resource.uri,
                    "name": resource.name,
                    "description": resource.description,
                    "mime_type": resource.mimeType
                }
                for resource in resources_result.resources
            ]
            
            # Get prompts (if supported)
            prompts = []
            try:
                prompts_result = await session.list_prompts()
                prompts = [
                    {
                        "name": prompt.name,
                        "description": prompt.description,
                        "arguments": prompt.arguments
                    }
                    for prompt in prompts_result.prompts
                ]
            except Exception:
                # Prompts not supported
                pass
            
            return {
                "server_name": server_name,
                "capabilities": capabilities.__dict__ if capabilities else {},
                "tools": tools,
                "resources": resources,
                "prompts": prompts,
                "tools_count": len(tools),
                "resources_count": len(resources),
                "prompts_count": len(prompts)
            }
            
        except Exception as e:
            logger.error(f"Failed to get server info for {server_name}: {e}")
            raise
    
    async def stream_resource_content(
        self,
        server_name: str,
        resource_uri: str
    ) -> AsyncGenerator[str, None]:
        """Stream resource content (for large resources)"""
        if server_name not in self.sessions:
            raise ValueError(f"No session found for server: {server_name}")
        
        session = self.sessions[server_name]
        
        try:
            # Read resource
            result = await session.read_resource(resource_uri)
            
            # For now, yield the content in chunks
            # In future SDK versions, this might support true streaming
            content = ""
            if result.contents:
                for content_item in result.contents:
                    if hasattr(content_item, 'text'):
                        content += content_item.text
                    elif hasattr(content_item, 'blob'):
                        content += str(content_item.blob)
            
            # Yield in chunks
            chunk_size = 1024
            for i in range(0, len(content), chunk_size):
                yield content[i:i + chunk_size]
                await asyncio.sleep(0)  # Allow other tasks to run
                
        except Exception as e:
            logger.error(f"Failed to stream resource {resource_uri}: {e}")
            raise
    
    async def close(self):
        """Close all connections and clean up resources"""
        try:
            # Cancel subscriptions
            for subscription_id in list(self.subscriptions.keys()):
                self.subscriptions[subscription_id]["active"] = False
                del self.subscriptions[subscription_id]
            
            # Close sessions
            await self.exit_stack.aclose()
            self.sessions.clear()
            
            logger.info("Enhanced MCP client closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing MCP client: {e}")

# Example usage
async def example_usage():
    """Example of how to use the enhanced MCP client"""
    client = EnhancedMCPClient()
    
    try:
        # Connect to a server
        await client.connect_stdio_server(
            "example-server",
            "python",
            ["-m", "example_mcp_server"]
        )
        
        # Get server info
        server_info = await client.get_enhanced_server_info("example-server")
        print(f"Connected to server with {server_info['tools_count']} tools")
        
        # Call a tool with progress
        result = await client.call_tool_with_progress(
            "example-server",
            "example-tool",
            {"param": "value"}
        )
        
        # Batch tool calls
        batch_results = await client.batch_tool_calls(
            "example-server",
            [
                {"name": "tool1", "arguments": {"param": "value1"}},
                {"name": "tool2", "arguments": {"param": "value2"}}
            ]
        )
        
        print(f"Batch executed {len(batch_results)} tools")
        
    finally:
        await client.close()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(example_usage())
