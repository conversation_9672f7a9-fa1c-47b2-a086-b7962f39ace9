"""
Enhanced MCP Manager with Bedrock Session Management

Async-first mixin aligned with Bedrock-backed session_manager integration.
- Uses ChatSession for context/history
- Builds valid Converse toolConfig (JSON schema object)
- Persists turns to Bedrock via ChatSession.add_turn (which writes invocation steps)
"""

import logging
import os
import json
import asyncio
from typing import Dict, List, Any, Optional
from session_manager_new import session_manager

logger = logging.getLogger(__name__)

class EnhancedMCPMixin:
    """
    Mixin class to add Bedrock session-aware functionality to existing MCPClientManager.
    Uses native Bedrock session management (via session_manager) for context retention.
    """
    
    # Default; manager overrides from env
    model_id: str = "apac.amazon.nova-lite-v1:0"
    
    async def chat_with_bedrock_with_context(
        self,
        message: str,
        session_id: str,
        tools_available: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Enhanced Bedrock chat with native session context retention using session_manager.
        """
        try:
            chat_session = session_manager.get_or_create_session(session_id)
            historical_messages = chat_session.get_bedrock_messages(max_turns=8)
            
            current_messages = historical_messages + [{
                "role": "user",
                "content": [{"text": message}]
            }]
            
            system_message = self._build_context_aware_system_message(chat_session, tools_available)
            tool_config = self._build_tool_config_for_bedrock(tools_available)
            
            result = await self._execute_contextual_conversation(
                messages=current_messages,
                system_message=system_message,
                tool_config=tool_config,
                session_id=session_id,
                model_id=self.model_id,
            )
            
            chat_session.add_turn(
                user_message=message,
                assistant_response=result["response"],
                tools_used=result.get("tools_used", []),
            )
            
            logger.info(f"Completed contextual chat for session {session_id}: {len(result.get('tools_used', []))} tools used")
            
            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "session_id": session_id
            }
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error in contextual chat for session {session_id}: {error_msg}")
            
            # Enhanced error handling
            if "ValidationException" in error_msg:
                if "model" in error_msg.lower():
                    response_text = f"Model validation error. Please check your BEDROCK_MODEL_ID in .env file. Current model: {self.model_id}"
                elif "region" in error_msg.lower():
                    response_text = f"Region validation error. Please check your AWS_REGION configuration. Current region: {os.getenv('AWS_REGION', 'not set')}"
                else:
                    response_text = f"Bedrock API validation error: {error_msg}"
            elif "RetryError" in error_msg:
                response_text = "Connection retry failed. Please check AWS credentials and region configuration."
            elif "AccessDenied" in error_msg:
                response_text = "Access denied. Please check your AWS permissions for Bedrock services."
            elif "ResourceNotFound" in error_msg:
                response_text = f"Resource not found. Please verify your model ID: {self.model_id}"
            else:
                response_text = f"I apologize, but I encountered an error: {error_msg}"
            
            return {
                "response": response_text,
                "tools_used": [],
                "session_id": session_id,
                "error": True
            }

    def _build_context_aware_system_message(
        self,
        chat_session,
        tools_available: Optional[List[str]] = None
    ) -> str:
        """Build context-aware system message with defensive check."""
        context = ""
        if hasattr(chat_session, "get_context_for_bedrock") and callable(chat_session.get_context_for_bedrock):
            context = chat_session.get_context_for_bedrock()
        
        tool_hint = ""
        if tools_available:
            tool_hint = (
                "\nYou have access to tools and should use them iteratively when needed. "
                "You can batch independent tool calls in parallel. "
                "Continue using tools until you have all the information needed for a complete answer. "
                "Only stop when you are confident you can provide a final, comprehensive response."
            )
        
        system = (
            "You are an assistant inside an MCP Bot. "
            "Answer precisely and use tools iteratively to gather complete information."
            f"\n\nSession Context:\n{context}"
            f"{tool_hint}"
        )
        
        return system

    def _build_tool_config_for_bedrock(self, tools_available: Optional[List[str]] = None) -> Optional[Dict]:
        """Enhanced tool configuration building with MCP schema validation."""
        if not tools_available:
            return None

        available_tools = self.get_available_tools()
        tools: List[Dict[str, Any]] = []

        for tool_key in tools_available:
            if tool_key not in available_tools:
                continue

            tool_data = available_tools[tool_key]
            tool = tool_data["tool"]

            # Enhanced schema processing with MCP patterns
            input_schema = tool.get("input_schema") or {"type": "object", "properties": {}, "required": []}

            # Validate and enhance schema using MCP patterns
            validated_schema = self._validate_and_enhance_schema(input_schema, tool["name"])

            tools.append({
                "toolSpec": {
                    "name": tool["name"],
                    "description": tool.get("description") or f"Tool from server {tool_data['server']}",
                    "inputSchema": {"json": validated_schema}
                }
            })

        if not tools:
            return None

        return {"tools": tools, "toolChoice": {"auto": {}}}

    async def _execute_contextual_conversation(
        self,
        messages: List[Dict[str, Any]],
        system_message: str,
        tool_config: Optional[Dict],
        session_id: str,
        model_id: str,
    ) -> Dict[str, Any]:
        """Execute contextual conversation with multi-iteration tool loop."""
        runtime = await self.get_async_bedrock_runtime()
        inference_config = {"temperature": 0.4, "topP": 0.9}
        
        msgs = list(messages)
        tools_used: List[Dict[str, Any]] = []
        max_iterations = 15  # prevent infinite loops
        
        for iteration in range(max_iterations):
            try:
                req = {
                    "modelId": model_id,
                    "messages": msgs,
                    "system": [{"text": system_message}],
                    "inferenceConfig": inference_config,
                }
                
                if tool_config:
                    req["toolConfig"] = tool_config
                
                resp = await runtime.converse(**req)
                output = resp.get("output", {}).get("message", {})
                content = output.get("content", [])
                stop_reason = resp.get("stopReason")
                
                logger.debug(f"Iteration {iteration + 1}: stop_reason={stop_reason}")
                
                if stop_reason == "tool_use":
                    # Record assistant toolUse
                    msgs.append({"role": "assistant", "content": content})
                    
                    # Execute tool calls in parallel
                    planned_calls = [
                        {
                            "name": b["toolUse"]["name"],
                            "input": b["toolUse"].get("input", {}),
                            "toolUseId": b["toolUse"].get("toolUseId"),
                        }
                        for b in content
                        if "toolUse" in b
                    ]
                    
                    exec_results = await self._execute_tool_calls(planned_calls, session_id)
                    tools_used.extend(exec_results)
                    
                    # Provide toolResult blocks back to model as JSON
                    tool_result_blocks = []
                    for call, res in zip(planned_calls, exec_results):
                        payload = {"success": res.get("success", False)}
                        
                        if res.get("success"):
                            result_data = res.get("result", "")
                            try:
                                parsed = json.loads(result_data) if isinstance(result_data, str) else result_data
                                payload["result"] = parsed
                            except Exception:
                                payload["result"] = result_data
                        else:
                            payload["error"] = res.get("error", "Unknown error")
                        
                        tool_result_blocks.append({
                            "toolResult": {
                                "toolUseId": call["toolUseId"],
                                "content": [{"json": payload}]
                            }
                        })
                    
                    msgs.append({"role": "user", "content": tool_result_blocks})
                    continue
                
                elif stop_reason in ("end_turn", "stop_sequence", "max_tokens"):
                    final_text_parts = [b["text"] for b in content if "text" in b]
                    final_text = "\n".join(final_text_parts).strip() if final_text_parts else ""
                    # Filter out thinking content before returning
                    filtered_text = self._filter_thinking_content(final_text)
                    return {"response": filtered_text, "tools_used": tools_used, "session_id": session_id}

                else:
                    final_text_parts = [b["text"] for b in content if "text" in b]
                    final_text = "\n".join(final_text_parts).strip() if final_text_parts else f"Response completed with stop reason: {stop_reason}"
                    # Filter out thinking content before returning
                    filtered_text = self._filter_thinking_content(final_text)
                    return {"response": filtered_text, "tools_used": tools_used, "session_id": session_id}
                    
            except Exception as e:
                logger.error(f"Error in conversation iteration {iteration + 1}: {e}")
                if iteration == 0:  # If first iteration fails, return error
                    raise
                # Otherwise, return what we have so far
                return {"response": f"Partial response due to error: {str(e)}", "tools_used": tools_used, "session_id": session_id}
        
        return {"response": "Response completed after maximum iterations.", "tools_used": tools_used, "session_id": session_id}

    def _filter_thinking_content(self, text: str) -> str:
        """
        Filter out thinking content from model responses.
        Removes content between <thinking> and </thinking> tags and other reasoning patterns.
        """
        import re

        if not text:
            return text

        # Remove thinking tags and their content
        # This pattern matches <thinking>...</thinking> including multiline content
        thinking_pattern = r'<thinking>.*?</thinking>'
        filtered_text = re.sub(thinking_pattern, '', text, flags=re.DOTALL | re.IGNORECASE)

        # Also remove other common reasoning patterns that might leak through
        # Remove content between <reasoning> and </reasoning> tags
        reasoning_pattern = r'<reasoning>.*?</reasoning>'
        filtered_text = re.sub(reasoning_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Remove content between <analysis> and </analysis> tags
        analysis_pattern = r'<analysis>.*?</analysis>'
        filtered_text = re.sub(analysis_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Clean up any extra whitespace that might be left
        filtered_text = re.sub(r'\n\s*\n\s*\n', '\n\n', filtered_text)  # Replace multiple newlines with double newlines
        filtered_text = filtered_text.strip()

        return filtered_text

    async def get_async_bedrock_runtime(self):
        """Get async bedrock runtime client."""
        try:
            import aioboto3
        except ImportError:
            raise ImportError("aioboto3 is required for async Bedrock operations. Install with: pip install aioboto3")
        
        session = aioboto3.Session()
        client = session.client("bedrock-runtime")
        return await client.__aenter__()

    async def _execute_tool_calls(self, tool_calls: List[Dict], session_id: str) -> List[Dict]:
        """Enhanced tool execution using MCP SDK patterns with better error handling and recovery"""
        tools_used = []

        # Group tools by server for efficient batch execution
        tools_by_server = {}
        for tool_call in tool_calls:
            tool_name = tool_call.get("name")
            server_name = self._find_server_for_tool(tool_name)
            if server_name not in tools_by_server:
                tools_by_server[server_name] = []
            tools_by_server[server_name].append(tool_call)

        # Execute tools per server with enhanced error handling
        for server_name, server_tools in tools_by_server.items():
            if not server_name:
                # Handle tools not found
                for tool_call in server_tools:
                    tools_used.append({
                        "tool_name": tool_call.get("name"),
                        "server_name": None,
                        "input": tool_call.get("input", {}),
                        "success": False,
                        "error": f"Tool {tool_call.get('name')} not found",
                        "session_id": session_id,
                        "toolUseId": tool_call.get("toolUseId"),
                    })
                continue

            connection = self.connections.get(server_name)
            if not connection:
                for tool_call in server_tools:
                    tools_used.append({
                        "tool_name": tool_call.get("name"),
                        "server_name": server_name,
                        "input": tool_call.get("input", {}),
                        "success": False,
                        "error": f"Server {server_name} not available",
                        "session_id": session_id,
                        "toolUseId": tool_call.get("toolUseId"),
                    })
                continue

            # Ensure connection is healthy before execution
            if not await connection.ensure_connected():
                for tool_call in server_tools:
                    tools_used.append({
                        "tool_name": tool_call.get("name"),
                        "server_name": server_name,
                        "input": tool_call.get("input", {}),
                        "success": False,
                        "error": f"Failed to connect to server {server_name}",
                        "session_id": session_id,
                        "toolUseId": tool_call.get("toolUseId"),
                    })
                continue

            # Execute tools on this server
            for tool_call in server_tools:
                tool_name = tool_call.get("name")
                tool_input = tool_call.get("input", {})
                tool_use_id = tool_call.get("toolUseId")

                logger.info(f"Executing tool: {tool_name} on server: {server_name} with input: {tool_input}")

                try:
                    # Use enhanced tool execution with SDK patterns
                    result = await self._execute_single_tool_enhanced(
                        connection, tool_name, tool_input, tool_use_id, session_id
                    )
                    tools_used.append(result)

                except Exception as e:
                    logger.error(f"Tool execution exception for {tool_name}: {e}")
                    tools_used.append({
                        "tool_name": tool_name,
                        "server_name": server_name,
                        "input": tool_input,
                        "success": False,
                        "error": f"Execution exception: {str(e)}",
                        "session_id": session_id,
                        "toolUseId": tool_use_id,
                        "error_type": type(e).__name__
                    })

        return tools_used

    async def _execute_single_tool_enhanced(self, connection, tool_name: str, tool_input: Dict, tool_use_id: str, session_id: str) -> Dict:
        """Execute a single tool with enhanced error handling and recovery"""
        start_time = asyncio.get_event_loop().time()

        try:
            # Use the enhanced call_tool method from connection
            result = await connection.call_tool(tool_name, tool_input)
            execution_time = asyncio.get_event_loop().time() - start_time

            usage = {
                "tool_name": tool_name,
                "server_name": connection.config.name,
                "input": tool_input,
                "success": result.get("success", False),
                "session_id": session_id,
                "toolUseId": tool_use_id,
                "execution_time": execution_time,
                "metadata": result.get("metadata", {})
            }

            if usage["success"]:
                usage["result"] = str(result.get("result", ""))
            else:
                usage["error"] = result.get("error", "Unknown error")
                usage["error_type"] = result.get("error_type", "UnknownError")

            return usage

        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"Enhanced tool execution failed for {tool_name}: {e}")

            # Check if it's a connection error and attempt recovery
            if "connection" in str(e).lower() or "transport" in str(e).lower():
                logger.warning(f"Connection error detected for {connection.config.name}, attempting recovery")
                if await connection.ensure_connected():
                    # Retry once after reconnection
                    try:
                        result = await connection.call_tool(tool_name, tool_input)
                        execution_time = asyncio.get_event_loop().time() - start_time

                        usage = {
                            "tool_name": tool_name,
                            "server_name": connection.config.name,
                            "input": tool_input,
                            "success": result.get("success", False),
                            "session_id": session_id,
                            "toolUseId": tool_use_id,
                            "execution_time": execution_time,
                            "retry_attempted": True,
                            "metadata": result.get("metadata", {})
                        }

                        if usage["success"]:
                            usage["result"] = str(result.get("result", ""))
                        else:
                            usage["error"] = result.get("error", "Unknown error")
                            usage["error_type"] = result.get("error_type", "UnknownError")

                        return usage

                    except Exception as retry_e:
                        logger.error(f"Retry also failed for {tool_name}: {retry_e}")

            return {
                "tool_name": tool_name,
                "server_name": connection.config.name,
                "input": tool_input,
                "success": False,
                "error": f"Enhanced execution failed: {str(e)}",
                "session_id": session_id,
                "toolUseId": tool_use_id,
                "execution_time": execution_time,
                "error_type": type(e).__name__
            }

    def _find_server_for_tool(self, tool_name: str) -> Optional[str]:
        """Find server that provides a tool."""
        available_tools = self.get_available_tools()
        for data in available_tools.values():
            if data["tool"]["name"] == tool_name:
                return data["server"]
        return None

    async def get_available_resources_enhanced(self) -> Dict[str, Dict]:
        """Get all available resources with enhanced metadata"""
        return self.get_available_resources()

    async def read_resource_enhanced(self, resource_uri: str, server_name: str = None) -> Optional[Dict]:
        """Enhanced resource reading with better error handling"""
        try:
            result = await self.read_resource(resource_uri, server_name)
            if result:
                logger.info(f"Successfully read resource {resource_uri} from server {result['server']}")
            return result
        except Exception as e:
            logger.error(f"Enhanced resource reading failed for {resource_uri}: {e}")
            return None

    async def list_resource_templates_enhanced(self, server_name: str = None) -> Dict[str, List]:
        """Enhanced resource template listing with error recovery"""
        try:
            templates = await self.list_resource_templates(server_name)
            logger.info(f"Retrieved resource templates from {len(templates)} servers")
            return templates
        except Exception as e:
            logger.error(f"Enhanced resource template listing failed: {e}")
            return {}

    async def discover_server_capabilities_enhanced(self, server_name: str) -> Dict[str, Any]:
        """Discover and return comprehensive server capabilities"""
        if server_name not in self.connections:
            return {"error": f"Server {server_name} not found"}

        connection = self.connections[server_name]

        # Ensure connection is healthy
        if not await connection.ensure_connected():
            return {"error": f"Failed to connect to server {server_name}"}

        try:
            capabilities = {
                "server_name": server_name,
                "status": connection.status,
                "tools": [],
                "resources": [],
                "resource_templates": [],
                "last_heartbeat": connection.last_heartbeat,
                "reconnect_attempts": connection.reconnect_attempts
            }

            # Get tools
            if connection.tools:
                capabilities["tools"] = [
                    {
                        "name": tool["name"],
                        "description": tool.get("description", ""),
                        "input_schema": tool.get("input_schema", {})
                    }
                    for tool in connection.tools
                ]

            # Get resources
            if connection.resources:
                capabilities["resources"] = [
                    {
                        "uri": resource["uri"],
                        "name": resource.get("name", ""),
                        "description": resource.get("description", ""),
                        "mime_type": resource.get("mimeType", "")
                    }
                    for resource in connection.resources
                ]

            # Get resource templates
            try:
                templates_result = await connection.session.list_resource_templates()
                if hasattr(templates_result, 'resourceTemplates'):
                    capabilities["resource_templates"] = [
                        {
                            "uri_template": template.uriTemplate,
                            "name": getattr(template, 'name', ''),
                            "description": getattr(template, 'description', ''),
                            "annotations": getattr(template, 'annotations', {})
                        }
                        for template in templates_result.resourceTemplates
                    ]
            except Exception as e:
                logger.warning(f"Failed to get resource templates from {server_name}: {e}")
                capabilities["resource_templates"] = []

            return capabilities

        except Exception as e:
            logger.error(f"Failed to discover capabilities for {server_name}: {e}")
            return {"error": f"Capability discovery failed: {str(e)}"}

    async def get_resource_content_with_context(self, resource_uri: str, session_id: str, server_name: str = None) -> Dict[str, Any]:
        """Get resource content with session context for enhanced responses"""
        try:
            # Read the resource
            resource_data = await self.read_resource_enhanced(resource_uri, server_name)

            if not resource_data:
                return {
                    "success": False,
                    "error": f"Failed to read resource {resource_uri}",
                    "session_id": session_id
                }

            # Get session context for enhanced processing
            chat_session = session_manager.get_or_create_session(session_id)
            context = ""
            if hasattr(chat_session, "get_context_for_bedrock") and callable(chat_session.get_context_for_bedrock):
                context = chat_session.get_context_for_bedrock()

            return {
                "success": True,
                "resource_uri": resource_uri,
                "content": resource_data["content"],
                "server": resource_data["server"],
                "metadata": resource_data.get("metadata", {}),
                "session_context": context,
                "session_id": session_id
            }

        except Exception as e:
            logger.error(f"Failed to get resource content with context for {resource_uri}: {e}")
            return {
                "success": False,
                "error": f"Resource access failed: {str(e)}",
                "session_id": session_id
            }

    def _validate_and_enhance_schema(self, schema: Dict[str, Any], tool_name: str) -> Dict[str, Any]:
        """Enhanced schema validation and enhancement using MCP patterns"""
        try:
            # Ensure basic schema structure
            if not isinstance(schema, dict):
                logger.warning(f"Invalid schema type for tool {tool_name}, using default")
                return {"type": "object", "properties": {}, "required": []}

            # Set default type if missing
            if "type" not in schema:
                schema["type"] = "object"

            # Ensure properties exist for object types
            if schema.get("type") == "object" and "properties" not in schema:
                schema["properties"] = {}

            # Validate required fields exist in properties
            if "required" in schema and "properties" in schema:
                valid_required = []
                for req_field in schema["required"]:
                    if req_field in schema["properties"]:
                        valid_required.append(req_field)
                    else:
                        logger.warning(f"Required field '{req_field}' not found in properties for tool {tool_name}")
                schema["required"] = valid_required

            # Enhanced property validation
            if "properties" in schema:
                schema["properties"] = self._validate_schema_properties(schema["properties"], tool_name)

            # Add MCP-specific enhancements
            schema = self._add_mcp_schema_enhancements(schema, tool_name)

            return schema

        except Exception as e:
            logger.error(f"Schema validation failed for tool {tool_name}: {e}")
            return {"type": "object", "properties": {}, "required": []}

    def _validate_schema_properties(self, properties: Dict[str, Any], tool_name: str) -> Dict[str, Any]:
        """Validate and enhance schema properties"""
        validated_properties = {}

        for prop_name, prop_schema in properties.items():
            if not isinstance(prop_schema, dict):
                logger.warning(f"Invalid property schema for {prop_name} in tool {tool_name}")
                validated_properties[prop_name] = {"type": "string", "description": "Auto-corrected property"}
                continue

            # Ensure type is specified
            if "type" not in prop_schema:
                prop_schema["type"] = "string"  # Default to string

            # Validate type values
            valid_types = ["string", "number", "integer", "boolean", "array", "object", "null"]
            if prop_schema["type"] not in valid_types:
                logger.warning(f"Invalid type '{prop_schema['type']}' for property {prop_name} in tool {tool_name}")
                prop_schema["type"] = "string"

            # Add description if missing
            if "description" not in prop_schema:
                prop_schema["description"] = f"Parameter {prop_name} for tool {tool_name}"

            validated_properties[prop_name] = prop_schema

        return validated_properties

    def _add_mcp_schema_enhancements(self, schema: Dict[str, Any], tool_name: str) -> Dict[str, Any]:
        """Add MCP-specific schema enhancements"""
        # Add metadata for better tool discovery
        if "title" not in schema:
            schema["title"] = f"{tool_name} Input Schema"

        # Add MCP-specific annotations
        if "mcp_metadata" not in schema:
            schema["mcp_metadata"] = {
                "tool_name": tool_name,
                "validation_level": "enhanced",
                "sdk_version": "enhanced_mcp_manager_v2"
            }

        return schema

    def validate_tool_input_enhanced(self, tool_name: str, input_data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """Enhanced tool input validation with detailed error reporting"""
        errors = []

        try:
            available_tools = self.get_available_tools()
            tool_found = False

            for tool_key, tool_data in available_tools.items():
                if tool_data["tool"]["name"] == tool_name:
                    tool_found = True
                    schema = tool_data["tool"].get("input_schema", {})

                    if not schema:
                        return True, []  # No schema means no validation required

                    # Enhanced validation with MCP patterns
                    validation_result = self._perform_enhanced_validation(input_data, schema, tool_name)
                    return validation_result

            if not tool_found:
                errors.append(f"Tool {tool_name} not found in available tools")
                return False, errors

            return True, []

        except Exception as e:
            errors.append(f"Validation error: {str(e)}")
            return False, errors

    def _perform_enhanced_validation(self, input_data: Dict[str, Any], schema: Dict[str, Any], tool_name: str) -> tuple[bool, List[str]]:
        """Perform enhanced validation using MCP schema patterns"""
        errors = []

        try:
            # Import jsonschema if available
            try:
                from jsonschema import validate, ValidationError, Draft202012Validator

                # Use MCP's enhanced validation patterns
                validator = Draft202012Validator(schema)
                validation_errors = list(validator.iter_errors(input_data))

                if validation_errors:
                    for error in validation_errors[:5]:  # Limit to first 5 errors
                        error_path = '.'.join(str(p) for p in error.path) if error.path else 'root'
                        errors.append(f"Path: {error_path}, Error: {error.message}")
                    return False, errors

                return True, []

            except ImportError:
                # Fallback to enhanced basic validation
                return self._enhanced_basic_validation(input_data, schema, tool_name)

        except Exception as e:
            errors.append(f"Enhanced validation failed: {str(e)}")
            return False, errors

    def _enhanced_basic_validation(self, input_data: Dict[str, Any], schema: Dict[str, Any], tool_name: str) -> tuple[bool, List[str]]:
        """Enhanced basic validation when jsonschema is not available"""
        errors = []

        # Check required fields
        if "required" in schema:
            for req_field in schema["required"]:
                if req_field not in input_data:
                    errors.append(f"Missing required field: {req_field}")

        # Enhanced type checking for properties
        if "properties" in schema:
            for prop_name, prop_schema in schema["properties"].items():
                if prop_name in input_data:
                    expected_type = prop_schema.get("type")
                    if expected_type:
                        if not self._check_enhanced_type(input_data[prop_name], expected_type, prop_name):
                            errors.append(f"Field {prop_name} has incorrect type, expected {expected_type}")

        return len(errors) == 0, errors

    def _check_enhanced_type(self, value: Any, expected_type: str, field_name: str) -> bool:
        """Enhanced type checking with better error detection"""
        type_map = {
            "string": str,
            "number": (int, float),
            "integer": int,
            "boolean": bool,
            "array": list,
            "object": dict,
            "null": type(None)
        }

        expected_python_type = type_map.get(expected_type)
        if expected_python_type:
            is_valid = isinstance(value, expected_python_type)
            if not is_valid:
                logger.debug(f"Type mismatch for {field_name}: expected {expected_type}, got {type(value).__name__}")
            return is_valid

        logger.warning(f"Unknown type '{expected_type}' for field {field_name}, assuming valid")
        return True  # Unknown type, assume valid
