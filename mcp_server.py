"""
MCP Server Implementation for AWS Bedrock Chat Bot

This server exposes the AWS Bedrock chat functionality as MCP tools and resources,
allowing other MCP clients to interact with your AI assistant.
"""

import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional, Sequence

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListResourcesRequest,
    ListResourcesResult,
    ListToolsRequest,
    ListToolsResult,
    ReadResourceRequest,
    ReadResourceResult,
    Resource,
    TextContent,
    Tool,
)

# Import your existing components
from main import MCPClientManager
from enhanced_mcp_manager import EnhancedMCPMixin
from session_manager_new import session_manager

logger = logging.getLogger(__name__)

class BedrockMCPServer:
    """MCP Server that exposes AWS Bedrock chat capabilities"""
    
    def __init__(self):
        self.server = Server("bedrock-chat-server")
        self.mcp_manager = None
        self._setup_handlers()
    
    async def initialize(self):
        """Initialize the MCP client manager"""
        try:
            # Create enhanced MCP manager
            class EnhancedMCPManager(MCPClientManager, EnhancedMCPMixin):
                pass
            
            self.mcp_manager = EnhancedMCPManager()
            await self.mcp_manager.initialize()
            logger.info("Bedrock MCP Server initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize MCP manager: {e}")
            raise
    
    def _setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available tools"""
            return [
                Tool(
                    name="bedrock_chat",
                    description="Chat with AWS Bedrock AI assistant with access to AWS tools",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "message": {
                                "type": "string",
                                "description": "The message to send to the AI assistant"
                            },
                            "session_id": {
                                "type": "string",
                                "description": "Session ID for conversation context",
                                "default": "default"
                            },
                            "tools_available": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "List of tool names to make available for this chat",
                                "default": []
                            }
                        },
                        "required": ["message"]
                    }
                ),
                Tool(
                    name="list_aws_tools",
                    description="List all available AWS tools from connected MCP servers",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                ),
                Tool(
                    name="get_session_context",
                    description="Get conversation context for a session",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "session_id": {
                                "type": "string",
                                "description": "Session ID to get context for"
                            }
                        },
                        "required": ["session_id"]
                    }
                ),
                Tool(
                    name="execute_aws_tool",
                    description="Execute a specific AWS tool directly",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "tool_name": {
                                "type": "string",
                                "description": "Name of the AWS tool to execute"
                            },
                            "tool_input": {
                                "type": "object",
                                "description": "Input parameters for the tool"
                            },
                            "session_id": {
                                "type": "string",
                                "description": "Session ID for context",
                                "default": "default"
                            }
                        },
                        "required": ["tool_name", "tool_input"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            if not self.mcp_manager:
                return [TextContent(type="text", text="Error: MCP manager not initialized")]
            
            try:
                if name == "bedrock_chat":
                    result = await self._handle_bedrock_chat(arguments)
                elif name == "list_aws_tools":
                    result = await self._handle_list_aws_tools(arguments)
                elif name == "get_session_context":
                    result = await self._handle_get_session_context(arguments)
                elif name == "execute_aws_tool":
                    result = await self._handle_execute_aws_tool(arguments)
                else:
                    result = {"error": f"Unknown tool: {name}"}
                
                return [TextContent(type="text", text=json.dumps(result, indent=2))]
                
            except Exception as e:
                logger.error(f"Error handling tool {name}: {e}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]
        
        @self.server.list_resources()
        async def handle_list_resources() -> List[Resource]:
            """List available resources"""
            return [
                Resource(
                    uri="bedrock://sessions",
                    name="Active Sessions",
                    description="List of active chat sessions",
                    mimeType="application/json"
                ),
                Resource(
                    uri="bedrock://aws-servers",
                    name="AWS MCP Servers",
                    description="Status of connected AWS MCP servers",
                    mimeType="application/json"
                ),
                Resource(
                    uri="bedrock://capabilities",
                    name="Server Capabilities",
                    description="Complete capabilities of all connected servers",
                    mimeType="application/json"
                )
            ]
        
        @self.server.read_resource()
        async def handle_read_resource(uri: str) -> str:
            """Read resource content"""
            if not self.mcp_manager:
                return json.dumps({"error": "MCP manager not initialized"})
            
            try:
                if uri == "bedrock://sessions":
                    # Get active sessions info
                    sessions_info = {
                        "active_sessions": list(session_manager.sessions.keys()),
                        "total_sessions": len(session_manager.sessions)
                    }
                    return json.dumps(sessions_info, indent=2)
                
                elif uri == "bedrock://aws-servers":
                    # Get AWS server status
                    server_status = {}
                    for name, connection in self.mcp_manager.connections.items():
                        server_status[name] = {
                            "status": connection.status,
                            "last_heartbeat": connection.last_heartbeat,
                            "reconnect_attempts": connection.reconnect_attempts,
                            "tools_count": len(connection.tools) if connection.tools else 0,
                            "resources_count": len(connection.resources) if connection.resources else 0
                        }
                    return json.dumps(server_status, indent=2)
                
                elif uri == "bedrock://capabilities":
                    # Get complete capabilities
                    capabilities = {}
                    for name in self.mcp_manager.connections.keys():
                        capabilities[name] = await self.mcp_manager.discover_server_capabilities_enhanced(name)
                    return json.dumps(capabilities, indent=2)
                
                else:
                    return json.dumps({"error": f"Unknown resource: {uri}"})
                    
            except Exception as e:
                logger.error(f"Error reading resource {uri}: {e}")
                return json.dumps({"error": str(e)})
    
    async def _handle_bedrock_chat(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Bedrock chat requests"""
        message = arguments.get("message", "")
        session_id = arguments.get("session_id", "default")
        tools_available = arguments.get("tools_available", [])
        
        if not message:
            return {"error": "Message is required"}
        
        # Use the enhanced chat method
        result = await self.mcp_manager.chat_with_bedrock_with_context(
            message=message,
            session_id=session_id,
            tools_available=tools_available
        )
        
        return result
    
    async def _handle_list_aws_tools(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle listing AWS tools"""
        available_tools = self.mcp_manager.get_available_tools()
        
        tools_by_server = {}
        for tool_key, tool_data in available_tools.items():
            server_name = tool_data["server"]
            if server_name not in tools_by_server:
                tools_by_server[server_name] = []
            
            tools_by_server[server_name].append({
                "name": tool_data["tool"]["name"],
                "description": tool_data["tool"].get("description", ""),
                "input_schema": tool_data["tool"].get("input_schema", {})
            })
        
        return {
            "tools_by_server": tools_by_server,
            "total_tools": len(available_tools)
        }
    
    async def _handle_get_session_context(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle getting session context"""
        session_id = arguments.get("session_id", "")
        
        if not session_id:
            return {"error": "Session ID is required"}
        
        try:
            chat_session = session_manager.get_or_create_session(session_id)
            context = ""
            if hasattr(chat_session, "get_context_for_bedrock") and callable(chat_session.get_context_for_bedrock):
                context = chat_session.get_context_for_bedrock()
            
            return {
                "session_id": session_id,
                "context": context,
                "message_count": len(chat_session.get_bedrock_messages()) if hasattr(chat_session, "get_bedrock_messages") else 0
            }
        except Exception as e:
            return {"error": f"Failed to get session context: {str(e)}"}
    
    async def _handle_execute_aws_tool(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Handle direct AWS tool execution"""
        tool_name = arguments.get("tool_name", "")
        tool_input = arguments.get("tool_input", {})
        session_id = arguments.get("session_id", "default")
        
        if not tool_name:
            return {"error": "Tool name is required"}
        
        # Execute the tool directly
        tool_calls = [{
            "name": tool_name,
            "input": tool_input,
            "toolUseId": f"direct-{tool_name}-{session_id}"
        }]
        
        results = await self.mcp_manager._execute_tool_calls(tool_calls, session_id)
        
        if results:
            return results[0]  # Return first result
        else:
            return {"error": "No results from tool execution"}

async def main():
    """Main entry point for the MCP server"""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and initialize server
    bedrock_server = BedrockMCPServer()
    await bedrock_server.initialize()
    
    # Run the server
    async with stdio_server() as (read_stream, write_stream):
        await bedrock_server.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="bedrock-chat-server",
                server_version="1.0.0",
                capabilities=bedrock_server.server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                )
            )
        )

if __name__ == "__main__":
    asyncio.run(main())
