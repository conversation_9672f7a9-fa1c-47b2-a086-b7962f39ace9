"""
Enhanced Streamlit Frontend with ChatGPT-Style Sidebar
Provides a ChatGPT-like UI for the context-aware MCP bot with session management
"""

import streamlit as st
import requests
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
API_TIMEOUT = int(os.getenv("API_TIMEOUT", "120"))

# Page configuration
st.set_page_config(
    page_title="Enhanced AWS Cost Optimization Assistant",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# ChatGPT-style CSS
st.markdown("""
<style>
.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.new-chat-btn {
    background: #10a37f;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
}
.session-item {
    background: #f7f7f8;
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    cursor: pointer;
    border-left: 3px solid transparent;
    transition: all 0.2s;
}
.session-item:hover {
    background: #ececf1;
}
.session-item.active {
    background: #d1ecf1;
    border-left-color: #10a37f;
}
.session-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: #374151;
}
.session-preview {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
}
.session-meta {
    font-size: 11px;
    color: #9ca3af;
    display: flex;
    justify-content: space-between;
}
.context-indicator {
    background: linear-gradient(45deg, #1f4e79, #2e6da4);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    margin: 5px 0;
    display: inline-block;
}
.chat-message {
    padding: 1rem;
    margin: 0.5rem 0;
    border-radius: 0.5rem;
}
.user-message {
    background-color: #f0f2f6;
    margin-left: 2rem;
}
.assistant-message {
    background-color: #ffffff;
    border: 1px solid #e1e5e9;
}
</style>
""", unsafe_allow_html=True)

# Enhanced API Client Class
class EnhancedAPIClient:
    def __init__(self, base_url: str, timeout: int = 120):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout

    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request with error handling."""
        url = f"{self.base_url}{endpoint}"
        kwargs.setdefault('timeout', self.timeout)
        
        try:
            response = requests.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            st.error(f"API request failed: {e}")
            raise

    def make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Public method to make requests."""
        return self._make_request(method, endpoint, **kwargs)

    def chat_with_context(self, message: str, session_id: str) -> Dict[str, Any]:
        """Send chat message with context."""
        response = self._make_request("POST", "/chat", json={
            "message": message,
            "conversation_id": session_id,
            "use_tools": True
        })
        return response.json()

    def get_session_history(self, session_id: str) -> Dict[str, Any]:
        """Get session conversation history."""
        response = self._make_request("GET", f"/sessions/{session_id}/history")
        return response.json()

    def restore_and_continue_session(self, session_id: str) -> Dict[str, Any]:
        """Restore and continue a session."""
        response = self._make_request("POST", f"/sessions/restore/{session_id}/continue")
        return response.json()

    def clear_session(self, session_id: str) -> Dict[str, Any]:
        """Clear/delete a session."""
        response = self._make_request("DELETE", f"/sessions/{session_id}")
        return response.json()

    def list_sessions_with_titles(self) -> Dict[str, Any]:
        """List all sessions with titles and previews."""
        response = self._make_request("GET", "/sessions/list-with-titles")
        return response.json()

    def rename_session(self, session_id: str, new_title: str) -> Dict[str, Any]:
        """Rename a session."""
        response = self._make_request("POST", f"/sessions/{session_id}/rename", json={"new_title": new_title})
        return response.json()

# Initialize Enhanced API client
@st.cache_resource
def get_enhanced_api_client():
    return EnhancedAPIClient(API_BASE_URL, API_TIMEOUT)

# Enhanced session state initialization
def initialize_enhanced_session_state():
    if 'session_id' not in st.session_state:
        st.session_state.session_id = f"streamlit-{uuid.uuid4().hex[:12]}"
    if 'conversation_history' not in st.session_state:
        st.session_state.conversation_history = []
    if 'session_stats' not in st.session_state:
        st.session_state.session_stats = None
    if 'session_restored' not in st.session_state:
        st.session_state.session_restored = False
    if 'api_status' not in st.session_state:
        st.session_state.api_status = None

def render_chatgpt_style_sidebar():
    """Render ChatGPT-style session sidebar"""
    api_client = get_enhanced_api_client()
    
    # Header with New Chat button
    col1, col2 = st.sidebar.columns([3, 1])
    with col1:
        st.sidebar.markdown("### 💬 Chat Sessions")
    with col2:
        if st.button("➕", key="new_chat", help="New Chat"):
            # Create new session
            st.session_state.session_id = f"streamlit-{uuid.uuid4().hex[:12]}"
            st.session_state.conversation_history = []
            st.session_state.session_stats = None
            st.session_state.session_restored = False
            st.success("Started new chat!")
            st.rerun()
    
    # Load sessions with titles
    try:
        # Use session state to cache sessions and reduce API calls
        if 'cached_sessions' not in st.session_state:
            st.session_state.cached_sessions = None
            st.session_state.last_session_refresh = 0

        # Only refresh sessions every 60 seconds or when explicitly requested
        import time
        current_time = time.time()

        with st.sidebar.expander("🔄 Refresh Sessions", expanded=False):
            col1, col2 = st.columns([2, 1])
            with col1:
                if st.button("Refresh All", key="refresh_all"):
                    st.session_state.force_session_refresh = True
                    st.rerun()
            with col2:
                # Show cache status
                if st.session_state.get('cached_sessions'):
                    cache_age = int(current_time - st.session_state.last_session_refresh)
                    st.caption(f"Cache: {cache_age}s")
                else:
                    st.caption("No cache")
        should_refresh = (
            st.session_state.cached_sessions is None or
            current_time - st.session_state.last_session_refresh > 60 or
            st.session_state.get('force_session_refresh', False)
        )

        if should_refresh:
            try:
                with st.spinner("🔄 Refreshing sessions..."):
                    sessions_data = api_client.list_sessions_with_titles()
                    st.session_state.cached_sessions = sessions_data
                    st.session_state.last_session_refresh = current_time
                    st.session_state.force_session_refresh = False
            except Exception as e:
                st.sidebar.error(f"Failed to load sessions: {e}")
                sessions_data = st.session_state.cached_sessions or {"sessions": [], "count": 0}
        else:
            sessions_data = st.session_state.cached_sessions
        sessions = sessions_data.get("sessions", [])
        
        if sessions:
            # Group sessions by date
            today_sessions = []
            yesterday_sessions = []
            older_sessions = []
            
            today = datetime.now().date()
            yesterday = today - timedelta(days=1)
            
            for session in sessions:
                try:
                    if session.get("last_activity"):
                        # Handle ISO format datetime strings from FastAPI's jsonable_encoder
                        activity_str = session["last_activity"]

                        # Parse ISO format datetime (e.g., "2025-09-16T18:38:20.111521+00:00")
                        if "T" in activity_str:
                            # ISO format from FastAPI
                            activity_date = datetime.fromisoformat(activity_str.replace("Z", "+00:00")).date()
                        elif len(activity_str) >= 10:
                            # Fallback for other formats
                            activity_date = datetime.strptime(activity_str[:10], "%Y-%m-%d").date()
                        else:
                            activity_date = today  # Default to today if parsing fails

                        if activity_date == today:
                            today_sessions.append(session)
                        elif activity_date == yesterday:
                            yesterday_sessions.append(session)
                        else:
                            older_sessions.append(session)
                    else:
                        older_sessions.append(session)
                except Exception as e:
                    # If datetime parsing fails, put in older sessions
                    older_sessions.append(session)
            
            # Render session groups
            def render_session_group(title, sessions_list):
                if sessions_list:
                    st.sidebar.markdown(f"**{title}**")
                    for session in sessions_list:
                        render_session_item(session)
            
            def render_session_item(session):
                session_id = session["session_id"]
                is_current = session_id == st.session_state.session_id
                is_discoverable = session.get("is_discoverable", False)
                
                # Session container
                with st.sidebar.container():
                    # Session title and preview
                    title = session["title"]
                    preview = session["last_message_preview"]

                    # Format last activity for display
                    last_activity = session['last_activity']
                    if isinstance(last_activity, str) and "T" in last_activity:
                        # Parse ISO format and format for display
                        try:
                            dt = datetime.fromisoformat(last_activity.replace("Z", "+00:00"))
                            formatted_activity = dt.strftime("%m/%d %H:%M")
                        except:
                            formatted_activity = last_activity[:16].replace("T", " ")
                    else:
                        formatted_activity = str(last_activity)

                    meta_info = f"{session['turn_count']} turns • {formatted_activity}"
                    
                    # Status indicators
                    status_icon = "🔄" if is_discoverable else "💾"
                    active_indicator = "🟢" if is_current else ""
                    
                    # Create expandable session item
                    with st.expander(f"{status_icon} {active_indicator} {title[:30]}{'...' if len(title) > 30 else ''}", expanded=False):
                        st.markdown(f"""
                        <div class="session-item {'active' if is_current else ''}">
                            <div class="session-preview">{preview}</div>
                            <div class="session-meta">
                                <span>{meta_info}</span>
                                <span>🛠️ {session['tools_used']}</span>
                            </div>
                        </div>
                        """, unsafe_allow_html=True)
                        
                        # Action buttons
                        col1, col2, col3 = st.columns(3)
                        
                        with col1:
                            if st.button("📂", key=f"load_{session_id}", help="Load Session"):
                                load_session(session_id, is_discoverable)
                        
                        with col2:
                            if st.button("✏️", key=f"rename_{session_id}", help="Rename"):
                                st.session_state[f"renaming_{session_id}"] = True
                        
                        with col3:
                            if st.button("🗑️", key=f"delete_{session_id}", help="Delete"):
                                delete_session(session_id)
                        
                        # Rename input
                        if st.session_state.get(f"renaming_{session_id}", False):
                            new_title = st.text_input("New title:", value=title, key=f"title_{session_id}")
                            col1, col2 = st.columns(2)
                            with col1:
                                if st.button("Save", key=f"save_{session_id}"):
                                    rename_session(session_id, new_title)
                            with col2:
                                if st.button("Cancel", key=f"cancel_{session_id}"):
                                    st.session_state[f"renaming_{session_id}"] = False
                                    st.rerun()
            
            # Render session groups
            render_session_group("Today", today_sessions)
            render_session_group("Yesterday", yesterday_sessions)
            render_session_group("Older", older_sessions)
            
        else:
            st.sidebar.info("No chat sessions found")
            
    except Exception as e:
        st.sidebar.error(f"Failed to load sessions: {e}")

def load_session(session_id: str, is_discoverable: bool):
    """Load a specific session"""
    try:
        api_client = get_enhanced_api_client()

        if is_discoverable:
            # Restore from Bedrock
            with st.spinner("Restoring session..."):
                response = api_client.restore_and_continue_session(session_id)
        else:
            # Load existing session
            response = api_client.get_session_history(session_id)

        # Update session state
        st.session_state.session_id = session_id
        st.session_state.conversation_history = []
        st.session_state.session_restored = True

        # Load conversation history
        for turn in response.get("history", []):
            st.session_state.conversation_history.append({
                "user": turn["user_message"],
                "assistant": turn["assistant_response"],
                "timestamp": turn["timestamp"],
                "tools_used": turn.get("tools_used", [])
            })

        st.success(f"Loaded session with {len(st.session_state.conversation_history)} messages!")
        # Only refresh sessions if needed
        st.session_state.force_session_refresh = True
        st.rerun()

    except Exception as e:
        st.error(f"Failed to load session: {e}")

def rename_session(session_id: str, new_title: str):
    """Rename a session"""
    try:
        api_client = get_enhanced_api_client()
        api_client.rename_session(session_id, new_title)
        st.success(f"Session renamed to '{new_title}'")
        st.session_state[f"renaming_{session_id}"] = False
        st.session_state.force_session_refresh = True
        st.rerun()
    except Exception as e:
        st.error(f"Failed to rename session: {e}")

def delete_session(session_id: str):
    """Delete a session"""
    try:
        api_client = get_enhanced_api_client()
        api_client.clear_session(session_id)
        st.success("Session deleted!")

        # If deleting current session, create new one
        if session_id == st.session_state.session_id:
            st.session_state.session_id = f"streamlit-{uuid.uuid4().hex[:12]}"
            st.session_state.conversation_history = []
            st.session_state.session_restored = False

        st.session_state.force_session_refresh = True
        st.rerun()
    except Exception as e:
        st.error(f"Failed to delete session: {e}")

def display_api_status():
    """Display API connection status"""
    api_client = get_enhanced_api_client()

    try:
        if st.session_state.api_status is None:
            with st.spinner("Checking API status..."):
                response = api_client.make_request("GET", "/")
                st.session_state.api_status = response.json()

        status = st.session_state.api_status
        if status:
            with st.expander("🔗 API Status", expanded=False):
                col1, col2 = st.columns(2)
                with col1:
                    st.metric("Status", "✅ Connected")
                    st.metric("Mode", status.get("mode", "unknown"))
                with col2:
                    session_stats = status.get("session_stats", {})
                    st.metric("Active Sessions", session_stats.get("total_sessions", 0))
                    st.metric("Total Conversations", session_stats.get("total_conversation_turns", 0))
    except Exception as e:
        st.error(f"API connection failed: {e}")
        st.session_state.api_status = None

def display_conversation_history():
    """Display conversation history with enhanced formatting"""
    for turn in st.session_state.conversation_history:
        # User message
        with st.chat_message("user"):
            st.write(turn["user"])

        # Assistant message
        with st.chat_message("assistant"):
            st.write(turn["assistant"])

            # Show tools used if any
            if turn.get("tools_used"):
                with st.expander(f"🛠️ Tools Used ({len(turn['tools_used'])})", expanded=False):
                    for tool in turn["tools_used"]:
                        st.caption(f"• {tool.get('tool_name', 'Unknown')} ({tool.get('server_name', 'Unknown')})")

def display_context_indicator(context_used: bool, session_stats: Optional[Dict]):
    """Display context usage indicator"""
    if context_used and session_stats:
        st.markdown(f"""
        <div class="context-indicator">
            🧠 Context Used: {session_stats.get('total_turns', 0)} turns,
            {session_stats.get('total_tools_used', 0)} tools
        </div>
        """, unsafe_allow_html=True)

def main():
    """Main application function"""
    initialize_enhanced_session_state()

    st.title("🤖 Enhanced AWS Cost Optimization Assistant")
    st.markdown("*AI-powered AWS analysis with conversation context, memory, and session discovery*")

    # Render ChatGPT-style sidebar
    render_chatgpt_style_sidebar()

    # Display API status
    display_api_status()

    # Main chat interface
    api_client = get_enhanced_api_client()

    # Show current session info
    if st.session_state.session_id:
        col1, col2, col3 = st.columns([2, 1, 1])
        with col1:
            st.caption(f"Session: {st.session_state.session_id[:12]}...")
        with col2:
            st.caption(f"Messages: {len(st.session_state.conversation_history)}")
        with col3:
            if st.session_state.session_restored:
                st.caption("🔄 Restored")

    # Display conversation history
    if st.session_state.conversation_history:
        display_conversation_history()

    # Chat input
    user_input = st.chat_input("Ask about AWS costs, pricing, or infrastructure...")

    if user_input:
        # Add user message to display immediately
        timestamp = datetime.now().isoformat()

        with st.chat_message("user"):
            st.write(user_input)

        with st.chat_message("assistant"):
            try:
                with st.spinner("Thinking..."):
                    response = api_client.chat_with_context(user_input, st.session_state.session_id)

                st.write(response["response"])

                # Store in conversation history
                st.session_state.conversation_history.append({
                    "user": user_input,
                    "assistant": response["response"],
                    "timestamp": timestamp,
                    "tools_used": response.get("tools_used", [])
                })

                # Display context indicator
                context_used = response.get("context_used", False)
                session_stats = response.get("session_stats")
                display_context_indicator(context_used, session_stats)

                if session_stats:
                    st.session_state.session_stats = session_stats

                # Only refresh sidebar if this is a new session or significant change
                if len(st.session_state.conversation_history) == 1:
                    # First message in session - refresh sidebar to show updated turn count
                    st.session_state.force_session_refresh = True

                st.rerun()

            except Exception as e:
                st.error(f"Error: {str(e)}")
                st.session_state.conversation_history.append({
                    "user": user_input,
                    "assistant": f"Error: {str(e)}",
                    "timestamp": timestamp,
                    "tools_used": []
                })

if __name__ == "__main__":
    main()
