# Core FastAPI and web framework dependencies
fastapi>=0.110.0
uvicorn[standard]>=0.30.6
pydantic>=2.9.2

# Environment and configuration
python-dotenv>=1.0.1

# AWS and Bedrock dependencies
boto3>=1.34.0
aioboto3>=12.0.0
aiobotocore>=2.11.0

# MCP (Model Context Protocol) dependencies - Enhanced
mcp[cli]>=1.14.0  # Latest version with FastMCP and CLI tools

# Async and concurrency
anyio>=4.6

# JSON schema validation (enhanced validation support)
jsonschema>=4.20.0

# HTTP requests and networking
requests>=2.31.0

# Data processing (for AWS cost analysis) - Optional
# pandas>=2.0.0
# numpy>=1.24.0

# Visualization (for cost dashboards) - Optional
# plotly>=5.15.0

# Streamlit (for web UI) - Optional
# streamlit>=1.30.0

# Type hints and development
typing-extensions>=4.8.0
