#!/usr/bin/env python3
"""
Quick start script for Bedrock MCP Server

This script starts your Bedrock MCP server and makes it available
for other MCP clients to connect to.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

async def main():
    """Start the Bedrock MCP server"""
    print("🚀 Starting AWS Bedrock MCP Server...")
    print("="*50)
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Import and run FastMCP server
        from fastmcp_server import main as fastmcp_main
        
        print("✓ Using FastMCP implementation")
        print("✓ Server will expose AWS Bedrock chat capabilities")
        print("✓ Available tools: bedrock_chat, list_aws_tools, execute_aws_tool")
        print("✓ Available resources: sessions, aws-servers, health")
        print("\n📡 Server starting on stdio transport...")
        print("   Connect using: mcp connect stdio python fastmcp_server.py")
        print("\n🛑 Press Ctrl+C to stop the server")
        print("-"*50)
        
        await fastmcp_main()
        
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        logging.error(f"Server startup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
