"""
FastMCP Server Implementation for AWS Bedrock Chat Bot

This is a high-level FastMCP implementation that provides a simpler,
decorator-based approach to exposing your AWS Bedrock capabilities.
"""

import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional

from mcp.server.fastmcp import FastMCP
from mcp.server.context import Context

# Import your existing components
from main import MCPClient<PERSON>anager
from enhanced_mcp_manager import EnhancedMCPMixin
from session_manager_new import session_manager

logger = logging.getLogger(__name__)

# Create FastMCP server instance
mcp = FastMCP("AWS Bedrock Chat Server")

# Global MCP manager instance
mcp_manager = None

async def initialize_mcp_manager():
    """Initialize the enhanced MCP manager"""
    global mcp_manager
    
    try:
        # Create enhanced MCP manager
        class EnhancedMCPManager(MCPClientManager, EnhancedMCPMixin):
            pass
        
        mcp_manager = EnhancedMCPManager()
        await mcp_manager.initialize()
        logger.info("FastMCP Bedrock server initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize MCP manager: {e}")
        raise

@mcp.tool()
async def bedrock_chat(
    ctx: Context,
    message: str,
    session_id: str = "default",
    tools_available: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Chat with AWS Bedrock AI assistant with access to AWS tools.
    
    Args:
        message: The message to send to the AI assistant
        session_id: Session ID for conversation context
        tools_available: List of tool names to make available for this chat
    
    Returns:
        Chat response with tools used and session information
    """
    if not mcp_manager:
        await ctx.info("Initializing MCP manager...")
        await initialize_mcp_manager()
    
    if not message.strip():
        raise ValueError("Message cannot be empty")
    
    await ctx.info(f"Processing chat message for session: {session_id}")
    
    # Use the enhanced chat method
    result = await mcp_manager.chat_with_bedrock_with_context(
        message=message,
        session_id=session_id,
        tools_available=tools_available or []
    )
    
    # Log progress
    tools_used_count = len(result.get("tools_used", []))
    if tools_used_count > 0:
        await ctx.info(f"Used {tools_used_count} AWS tools to generate response")
    
    return result

@mcp.tool()
async def list_aws_tools(ctx: Context) -> Dict[str, Any]:
    """
    List all available AWS tools from connected MCP servers.
    
    Returns:
        Dictionary containing tools organized by server
    """
    if not mcp_manager:
        await initialize_mcp_manager()
    
    await ctx.info("Retrieving available AWS tools...")
    
    available_tools = mcp_manager.get_available_tools()
    
    tools_by_server = {}
    for tool_key, tool_data in available_tools.items():
        server_name = tool_data["server"]
        if server_name not in tools_by_server:
            tools_by_server[server_name] = []
        
        tools_by_server[server_name].append({
            "name": tool_data["tool"]["name"],
            "description": tool_data["tool"].get("description", ""),
            "input_schema": tool_data["tool"].get("input_schema", {})
        })
    
    await ctx.info(f"Found {len(available_tools)} tools across {len(tools_by_server)} servers")
    
    return {
        "tools_by_server": tools_by_server,
        "total_tools": len(available_tools),
        "servers": list(tools_by_server.keys())
    }

@mcp.tool()
async def execute_aws_tool(
    ctx: Context,
    tool_name: str,
    tool_input: Dict[str, Any],
    session_id: str = "default"
) -> Dict[str, Any]:
    """
    Execute a specific AWS tool directly.
    
    Args:
        tool_name: Name of the AWS tool to execute
        tool_input: Input parameters for the tool
        session_id: Session ID for context
    
    Returns:
        Tool execution result
    """
    if not mcp_manager:
        await initialize_mcp_manager()
    
    if not tool_name.strip():
        raise ValueError("Tool name cannot be empty")
    
    await ctx.info(f"Executing AWS tool: {tool_name}")
    
    # Validate tool exists
    available_tools = mcp_manager.get_available_tools()
    tool_found = False
    for tool_data in available_tools.values():
        if tool_data["tool"]["name"] == tool_name:
            tool_found = True
            break
    
    if not tool_found:
        raise ValueError(f"Tool '{tool_name}' not found. Available tools: {list(t['tool']['name'] for t in available_tools.values())}")
    
    # Execute the tool
    tool_calls = [{
        "name": tool_name,
        "input": tool_input,
        "toolUseId": f"fastmcp-{tool_name}-{session_id}"
    }]
    
    results = await mcp_manager._execute_tool_calls(tool_calls, session_id)
    
    if results:
        result = results[0]
        if result.get("success"):
            await ctx.info(f"Tool {tool_name} executed successfully")
        else:
            await ctx.info(f"Tool {tool_name} execution failed: {result.get('error', 'Unknown error')}")
        return result
    else:
        raise RuntimeError("No results from tool execution")

@mcp.tool()
async def get_session_context(ctx: Context, session_id: str) -> Dict[str, Any]:
    """
    Get conversation context for a session.
    
    Args:
        session_id: Session ID to get context for
    
    Returns:
        Session context information
    """
    if not session_id.strip():
        raise ValueError("Session ID cannot be empty")
    
    await ctx.info(f"Retrieving context for session: {session_id}")
    
    try:
        chat_session = session_manager.get_or_create_session(session_id)
        context = ""
        if hasattr(chat_session, "get_context_for_bedrock") and callable(chat_session.get_context_for_bedrock):
            context = chat_session.get_context_for_bedrock()
        
        message_count = 0
        if hasattr(chat_session, "get_bedrock_messages"):
            message_count = len(chat_session.get_bedrock_messages())
        
        return {
            "session_id": session_id,
            "context": context,
            "message_count": message_count,
            "has_context": bool(context.strip())
        }
    except Exception as e:
        await ctx.info(f"Error retrieving session context: {str(e)}")
        raise RuntimeError(f"Failed to get session context: {str(e)}")

@mcp.tool()
async def get_server_capabilities(ctx: Context, server_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Get comprehensive capabilities of connected AWS MCP servers.
    
    Args:
        server_name: Specific server to get capabilities for (optional)
    
    Returns:
        Server capabilities information
    """
    if not mcp_manager:
        await initialize_mcp_manager()
    
    if server_name:
        await ctx.info(f"Getting capabilities for server: {server_name}")
        capabilities = await mcp_manager.discover_server_capabilities_enhanced(server_name)
        return {server_name: capabilities}
    else:
        await ctx.info("Getting capabilities for all connected servers")
        capabilities = {}
        for name in mcp_manager.connections.keys():
            capabilities[name] = await mcp_manager.discover_server_capabilities_enhanced(name)
        
        await ctx.info(f"Retrieved capabilities for {len(capabilities)} servers")
        return capabilities

# Resources
@mcp.resource("bedrock://sessions")
async def get_active_sessions(ctx: Context) -> str:
    """Get information about active chat sessions"""
    await ctx.info("Retrieving active sessions information")
    
    sessions_info = {
        "active_sessions": list(session_manager.sessions.keys()),
        "total_sessions": len(session_manager.sessions),
        "timestamp": asyncio.get_event_loop().time()
    }
    
    return json.dumps(sessions_info, indent=2)

@mcp.resource("bedrock://aws-servers")
async def get_aws_servers_status(ctx: Context) -> str:
    """Get status of connected AWS MCP servers"""
    if not mcp_manager:
        await initialize_mcp_manager()
    
    await ctx.info("Retrieving AWS servers status")
    
    server_status = {}
    for name, connection in mcp_manager.connections.items():
        server_status[name] = {
            "status": connection.status,
            "last_heartbeat": connection.last_heartbeat,
            "reconnect_attempts": connection.reconnect_attempts,
            "tools_count": len(connection.tools) if connection.tools else 0,
            "resources_count": len(connection.resources) if connection.resources else 0,
            "config": {
                "command": connection.config.command,
                "name": connection.config.name
            }
        }
    
    return json.dumps(server_status, indent=2)

@mcp.resource("bedrock://health")
async def get_health_status(ctx: Context) -> str:
    """Get overall health status of the Bedrock MCP system"""
    if not mcp_manager:
        await initialize_mcp_manager()
    
    await ctx.info("Checking system health")
    
    health_status = {
        "mcp_manager_initialized": mcp_manager is not None,
        "connected_servers": len(mcp_manager.connections) if mcp_manager else 0,
        "active_sessions": len(session_manager.sessions),
        "environment": {
            "aws_region": os.getenv("AWS_REGION", "not set"),
            "bedrock_model": os.getenv("BEDROCK_MODEL_ID", "not set")
        }
    }
    
    # Check server health
    if mcp_manager:
        healthy_servers = 0
        for connection in mcp_manager.connections.values():
            if connection.status == "connected":
                healthy_servers += 1
        
        health_status["healthy_servers"] = healthy_servers
        health_status["server_health_percentage"] = (healthy_servers / len(mcp_manager.connections) * 100) if mcp_manager.connections else 0
    
    return json.dumps(health_status, indent=2)

async def main():
    """Main entry point for the FastMCP server"""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Initialize MCP manager
    await initialize_mcp_manager()
    
    # Run the FastMCP server
    await mcp.run()

if __name__ == "__main__":
    asyncio.run(main())
