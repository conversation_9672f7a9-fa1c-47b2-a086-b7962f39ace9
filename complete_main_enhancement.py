#!/usr/bin/env python3
"""
Script to complete the enhancement of main.py with remaining MCP SDK features

This script adds the remaining enhanced methods to complete the MCP implementation.
"""

import os
import sys

def complete_main_enhancement():
    """Complete the enhancement of main.py"""
    
    # Read the current main.py
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the insertion point (after the last method in EnhancedMCPClientManager)
    insertion_point = content.rfind('            }')
    
    if insertion_point == -1:
        print("Could not find insertion point in main.py")
        return False
    
    # Find the end of the insertion point line
    insertion_point = content.find('\n', insertion_point) + 1
    
    # Enhanced methods to add
    enhanced_methods = '''
    def get_available_tools(self) -> Dict[str, Dict[str, Any]]:
        """Get all available tools with enhanced metadata and filtering"""
        tools = {}
        
        for tool_key, tool_info in self.global_tool_registry.items():
            server_name = tool_info["server"]
            connection = self.connections.get(server_name)

            # Only include tools from connected servers
            if connection and connection.status == "connected":
                tools[tool_key] = {
                    "server": server_name,
                    "tool": {
                        "name": tool_info["tool_name"],
                        "description": tool_info["description"],
                        "input_schema": tool_info["input_schema"],
                        "annotations": tool_info.get("annotations", {})
                    },
                    "connection": connection,
                    "metadata": tool_info.get("metadata", {}),
                    "validation_status": tool_info.get("validation_status", "unknown")
                }
        
        return tools

    async def call_tool_enhanced(
        self, 
        server_name: str, 
        tool_name: str, 
        arguments: Dict[str, Any],
        progress_callback: Optional[callable] = None,
        timeout: Optional[float] = None
    ) -> Dict[str, Any]:
        """Enhanced tool calling with progress tracking and comprehensive error handling"""
        if server_name not in self.connections:
            return {
                "success": False,
                "error": f"Server {server_name} not found",
                "tool_name": tool_name,
                "server_name": server_name,
                "available_servers": list(self.connections.keys())
            }

        connection = self.connections[server_name]

        # Use enhanced connection management
        if not await connection.ensure_connected():
            return {
                "success": False,
                "error": f"Server {server_name} is not connected: {connection.error}",
                "tool_name": tool_name,
                "server_name": server_name
            }

        return await connection.call_tool_enhanced(tool_name, arguments, progress_callback, timeout)

    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Legacy method - redirects to enhanced tool calling"""
        return await self.call_tool_enhanced(server_name, tool_name, arguments)

    async def read_resource_enhanced(
        self, 
        resource_uri: str, 
        server_name: str = None,
        progress_callback: Optional[callable] = None
    ) -> Optional[Dict]:
        """Enhanced resource reading with progress tracking"""
        connection = None

        if server_name:
            connection = self.connections.get(server_name)
            if not connection:
                logger.error(f"Server {server_name} not found")
                return None
        else:
            # Find server by resource URI
            for resource_key, resource_info in self.global_resource_registry.items():
                if resource_info["uri"] == resource_uri:
                    server_name = resource_info["server"]
                    connection = self.connections.get(server_name)
                    break

        if not connection:
            logger.error(f"No server found for resource {resource_uri}")
            return None

        return await connection.read_resource_enhanced(resource_uri, progress_callback)

    async def read_resource(self, resource_uri: str, server_name: str = None) -> Optional[Dict]:
        """Legacy method - redirects to enhanced resource reading"""
        return await self.read_resource_enhanced(resource_uri, server_name)

    def get_available_resources(self) -> Dict[str, Dict]:
        """Get all available resources with enhanced metadata"""
        return self.global_resource_registry.copy()

    def get_available_prompts(self) -> Dict[str, Dict]:
        """Get all available prompts with enhanced metadata"""
        return self.global_prompt_registry.copy()

    async def get_prompt_enhanced(
        self, 
        server_name: str, 
        prompt_name: str, 
        arguments: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Enhanced prompt execution with comprehensive error handling"""
        if server_name not in self.connections:
            return {
                "success": False,
                "error": f"Server {server_name} not found",
                "prompt_name": prompt_name,
                "server_name": server_name
            }

        connection = self.connections[server_name]

        if not await connection.ensure_connected():
            return {
                "success": False,
                "error": f"Server {server_name} is not connected",
                "prompt_name": prompt_name,
                "server_name": server_name
            }

        try:
            result = await connection.session.get_prompt(prompt_name, arguments or {})
            
            return {
                "success": True,
                "prompt_name": prompt_name,
                "server_name": server_name,
                "result": result,
                "metadata": getattr(result, 'meta', {}) if hasattr(result, 'meta') else {}
            }

        except Exception as e:
            logger.error(f"Failed to get prompt {prompt_name} from {server_name}: {e}")
            return {
                "success": False,
                "error": f"Prompt execution failed: {str(e)}",
                "prompt_name": prompt_name,
                "server_name": server_name,
                "error_type": type(e).__name__
            }

    async def get_server_capabilities_enhanced(self, server_name: str) -> Dict[str, Any]:
        """Get comprehensive server capabilities with enhanced metadata"""
        if server_name not in self.connections:
            return {"error": f"Server {server_name} not found"}

        connection = self.connections[server_name]

        if not await connection.ensure_connected():
            return {"error": f"Failed to connect to server {server_name}"}

        try:
            capabilities = {
                "server_name": server_name,
                "status": connection.status,
                "transport_type": connection.config.transport_type,
                "tools": [],
                "resources": [],
                "prompts": [],
                "connection_metrics": connection.connection_metrics.copy(),
                "last_heartbeat": connection.last_heartbeat,
                "reconnect_attempts": connection.reconnect_attempts,
                "uptime": time.time() - connection.connection_start_time if connection.connection_start_time else 0
            }

            # Get tools with enhanced metadata
            for tool_name, tool_info in connection.tool_registry.items():
                capabilities["tools"].append({
                    "name": tool_name,
                    "description": tool_info["description"],
                    "input_schema": tool_info["input_schema"],
                    "annotations": tool_info.get("annotations", {}),
                    "validation_status": tool_info.get("validation_status", "unknown")
                })

            # Get resources with enhanced metadata
            for resource_key, resource_info in connection.resource_registry.items():
                capabilities["resources"].append({
                    "uri": resource_info["uri"],
                    "name": resource_info["name"],
                    "description": resource_info["description"],
                    "mime_type": resource_info["mime_type"],
                    "annotations": resource_info.get("annotations", {}),
                    "last_accessed": resource_info.get("last_accessed"),
                    "subscription_count": resource_info.get("subscription_count", 0)
                })

            # Get prompts with enhanced metadata
            for prompt_name, prompt_info in connection.prompt_registry.items():
                capabilities["prompts"].append({
                    "name": prompt_name,
                    "description": prompt_info["description"],
                    "arguments": prompt_info["arguments"],
                    "annotations": prompt_info.get("annotations", {})
                })

            return capabilities

        except Exception as e:
            logger.error(f"Failed to get capabilities for {server_name}: {e}")
            return {"error": f"Capability discovery failed: {str(e)}"}

    async def get_connection_pool_status(self) -> Dict[str, Any]:
        """Get comprehensive connection pool status and statistics"""
        status = {
            "pool_stats": self.connection_pool_stats.copy(),
            "servers": {},
            "global_registries": {
                "tools": len(self.global_tool_registry),
                "resources": len(self.global_resource_registry),
                "prompts": len(self.global_prompt_registry)
            },
            "active_sessions": len(self.active_sessions),
            "resource_subscriptions": len(self.resource_subscriptions)
        }

        for server_name, connection in self.connections.items():
            status["servers"][server_name] = {
                "status": connection.status,
                "transport_type": connection.config.transport_type,
                "last_heartbeat": connection.last_heartbeat,
                "reconnect_attempts": connection.reconnect_attempts,
                "connection_metrics": connection.connection_metrics.copy(),
                "tools_count": len(connection.tools),
                "resources_count": len(connection.resources),
                "prompts_count": len(connection.prompts),
                "active_progress_tokens": len(connection.active_progress_tokens),
                "error": connection.error
            }

        return status

    async def cleanup(self):
        """Enhanced cleanup with comprehensive resource management"""
        cleanup_start = time.time()
        logger.info("Starting enhanced cleanup of all connections")
        
        # Get list of connections to avoid modification during iteration
        connections_to_cleanup = list(self.connections.items())
        
        # Cleanup each connection with enhanced error handling
        cleanup_results = []
        for name, connection in connections_to_cleanup:
            try:
                logger.info(f"Cleaning up connection: {name}")
                await connection.disconnect()
                cleanup_results.append({"server": name, "success": True})
            except Exception as e:
                logger.error(f"Error cleaning up connection {name}: {e}")
                cleanup_results.append({"server": name, "success": False, "error": str(e)})
        
        # Clear all registries and state
        self.connections.clear()
        self.global_tool_registry.clear()
        self.global_resource_registry.clear()
        self.global_prompt_registry.clear()
        self.resource_subscriptions.clear()
        self.active_sessions.clear()
        
        # Close async Bedrock client if exists
        if self.async_bedrock_client:
            try:
                await self.async_bedrock_client.__aexit__(None, None, None)
            except Exception as e:
                logger.warning(f"Error closing async Bedrock client: {e}")
        
        cleanup_time = (time.time() - cleanup_start) * 1000
        successful_cleanups = sum(1 for result in cleanup_results if result["success"])
        
        logger.info(f"Enhanced cleanup completed in {cleanup_time:.2f}ms")
        logger.info(f"Successfully cleaned up {successful_cleanups}/{len(cleanup_results)} connections")
        
        return {
            "cleanup_time": cleanup_time,
            "total_connections": len(cleanup_results),
            "successful_cleanups": successful_cleanups,
            "cleanup_results": cleanup_results
        }


# Create backward compatibility alias
MCPClientManager = EnhancedMCPClientManager
MCPServerConnection = EnhancedMCPServerConnection

# Update the global manager instance
mcp_manager = EnhancedMCPClientManager()
'''

    # Insert the enhanced methods
    new_content = content[:insertion_point] + enhanced_methods + content[insertion_point:]
    
    # Write the updated content
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ Successfully completed main.py enhancement!")
    print("📊 Added enhanced methods for:")
    print("   - Tool execution with progress tracking")
    print("   - Resource reading with content type detection")
    print("   - Prompt execution support")
    print("   - Server capability discovery")
    print("   - Connection pool management")
    print("   - Comprehensive cleanup")
    
    return True

if __name__ == "__main__":
    if complete_main_enhancement():
        print("\n🎉 MCP Enhancement Complete!")
        print("\nNext steps:")
        print("1. Install updated dependencies: pip install -r requirements.txt")
        print("2. Test the enhanced implementation: python test_mcp_implementation.py")
        print("3. Start your enhanced MCP server: python start_bedrock_mcp_server.py")
    else:
        print("❌ Enhancement failed!")
        sys.exit(1)
