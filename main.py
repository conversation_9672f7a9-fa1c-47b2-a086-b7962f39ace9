"""
Enhanced FastAPI Backend for MCP Client with Latest SDK Features

This implementation uses the latest MCP Python SDK features including:
- Enhanced client session management
- Progress tracking and monitoring
- Resource subscriptions
- Structured output validation
- Multiple transport protocols (stdio, SSE)
- Advanced error handling and recovery
- Comprehensive telemetry and observability
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field, field_validator
from typing import List, Dict, Any, Optional, Union, AsyncGenerator, Callable
import asyncio
import json
import logging
import time
from contextlib import asynccontextmanager, AsyncExitStack
import uvicorn
import boto3
from botocore.config import Config

# Enhanced MCP SDK imports with latest features
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client
from mcp.types import (
    ProgressToken,
    CallToolRequest,
    CallToolResult,
    ListResourcesRequest,
    ListResourcesResult,
    ReadResourceRequest,
    ReadResourceResult,
    GetPromptRequest,
    GetPromptResult,
    LoggingLevel,
    Resource,
    Tool,
    Prompt,
    TextContent,
    ImageContent,
    EmbeddedResource
)

import os
from dotenv import load_dotenv
import uuid
import anyio
from datetime import datetime, timezone

load_dotenv()



# Enhanced imports for validation and monitoring
try:
    from jsonschema import validate, Draft202012Validator, ValidationError
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False

try:
    import aioboto3
    AIOBOTO3_AVAILABLE = True
except ImportError:
    AIOBOTO3_AVAILABLE = False

# Enhanced telemetry and observability
class EnhancedTelemetryTracker:
    """Enhanced telemetry tracker with detailed performance monitoring and observability"""

    def __init__(self):
        self.metrics = {
            "total_requests": 0,
            "total_tool_calls": 0,
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_latency_ms": 0,
            "error_count": 0,
            "tool_execution_times": [],
            "conversation_turns": 0,
            "server_connections": {},
            "tool_usage_by_server": {},
            "error_types": {},
            "session_count": 0,
            "resource_reads": 0,
            "progress_events": 0
        }
        self.start_time = time.time()
        self.session_metrics = {}  # Per-session metrics

    def record_conversation_turn(self, usage: Dict, metrics: Dict, tool_count: int = 0, session_id: str = None):
        """Record enhanced metrics from a conversation turn"""
        self.metrics["total_requests"] += 1
        self.metrics["conversation_turns"] += 1
        self.metrics["total_tool_calls"] += tool_count
        self.metrics["total_input_tokens"] += usage.get("inputTokens", 0)
        self.metrics["total_output_tokens"] += usage.get("outputTokens", 0)
        self.metrics["total_latency_ms"] += metrics.get("latencyMs", 0)

        # Track per-session metrics
        if session_id:
            if session_id not in self.session_metrics:
                self.session_metrics[session_id] = {
                    "turns": 0, "tools_used": 0, "tokens": 0, "errors": 0
                }
            self.session_metrics[session_id]["turns"] += 1
            self.session_metrics[session_id]["tools_used"] += tool_count
            self.session_metrics[session_id]["tokens"] += usage.get("inputTokens", 0) + usage.get("outputTokens", 0)

    def record_error(self, error_type: str = "unknown", session_id: str = None):
        """Record an error with enhanced categorization"""
        self.metrics["error_count"] += 1
        self.metrics["error_types"][error_type] = self.metrics["error_types"].get(error_type, 0) + 1

        if session_id and session_id in self.session_metrics:
            self.session_metrics[session_id]["errors"] += 1

    def record_tool_execution(self, execution_time_ms: float, server_name: str, tool_name: str):
        """Record enhanced tool execution metrics"""
        self.metrics["tool_execution_times"].append(execution_time_ms)

        # Track by server
        if server_name not in self.metrics["tool_usage_by_server"]:
            self.metrics["tool_usage_by_server"][server_name] = {}
        if tool_name not in self.metrics["tool_usage_by_server"][server_name]:
            self.metrics["tool_usage_by_server"][server_name][tool_name] = {"count": 0, "total_time": 0}

        self.metrics["tool_usage_by_server"][server_name][tool_name]["count"] += 1
        self.metrics["tool_usage_by_server"][server_name][tool_name]["total_time"] += execution_time_ms

    def record_server_connection(self, server_name: str, status: str, connection_time_ms: float = 0):
        """Record server connection metrics"""
        if server_name not in self.metrics["server_connections"]:
            self.metrics["server_connections"][server_name] = {
                "attempts": 0, "successes": 0, "failures": 0, "total_connection_time": 0
            }

        self.metrics["server_connections"][server_name]["attempts"] += 1
        if status == "connected":
            self.metrics["server_connections"][server_name]["successes"] += 1
        else:
            self.metrics["server_connections"][server_name]["failures"] += 1
        self.metrics["server_connections"][server_name]["total_connection_time"] += connection_time_ms

    def record_resource_read(self):
        """Record resource read operation"""
        self.metrics["resource_reads"] += 1

    def record_progress_event(self):
        """Record progress tracking event"""
        self.metrics["progress_events"] += 1

    def get_summary(self) -> Dict[str, Any]:
        """Get comprehensive telemetry summary"""
        return {
            "total_requests": self.metrics["total_requests"],
            "session_count": self.metrics["session_count"],
            "resource_reads": self.metrics["resource_reads"],
            "progress_events": self.metrics["progress_events"],
            "server_connections": dict(self.metrics["server_connections"]),
            "tool_usage_by_server": dict(self.metrics["tool_usage_by_server"]),
            "error_types": dict(self.metrics["error_types"]),
            "performance_metrics": {
                "average_tool_execution_time": self._calculate_average_execution_time(),
                "total_errors": sum(self.metrics["error_types"].values()),
                "success_rate": self._calculate_success_rate()
            }
        }

    def _calculate_average_execution_time(self) -> float:
        """Calculate average tool execution time"""
        if not hasattr(self, '_execution_times'):
            self._execution_times = []
        return sum(self._execution_times) / len(self._execution_times) if self._execution_times else 0.0

    def _calculate_success_rate(self) -> float:
        """Calculate overall success rate"""
        total_requests = self.metrics["total_requests"]
        total_errors = sum(self.metrics["error_types"].values())
        return ((total_requests - total_errors) / total_requests * 100) if total_requests > 0 else 100.0

    def record_tool_execution_time(self, execution_time: float):
        """Record tool execution time for performance metrics"""
        if not hasattr(self, '_execution_times'):
            self._execution_times = []
        self._execution_times.append(execution_time)

        # Keep only last 1000 execution times to prevent memory growth
        if len(self._execution_times) > 1000:
            self._execution_times = self._execution_times[-1000:]

    def get_enhanced_summary(self) -> Dict:
        """Get comprehensive telemetry summary with enhanced metrics"""
        uptime = time.time() - self.start_time
        avg_latency = (self.metrics["total_latency_ms"] / max(1, self.metrics["total_requests"]))
        avg_tool_time = (sum(self.metrics["tool_execution_times"]) /
                        max(1, len(self.metrics["tool_execution_times"])))

        # Calculate server-specific metrics
        server_stats = {}
        for server, tools in self.metrics["tool_usage_by_server"].items():
            total_calls = sum(tool["count"] for tool in tools.values())
            total_time = sum(tool["total_time"] for tool in tools.values())
            avg_time = total_time / max(1, total_calls)
            server_stats[server] = {
                "total_calls": total_calls,
                "average_execution_time": avg_time,
                "tools": len(tools)
            }

        return {
            **self.metrics,
            "uptime_seconds": uptime,
            "average_latency_ms": avg_latency,
            "average_tool_execution_ms": avg_tool_time,
            "success_rate": 1 - (self.metrics["error_count"] / max(1, self.metrics["total_requests"])),
            "requests_per_second": self.metrics["total_requests"] / max(1, uptime),
            "server_statistics": server_stats,
            "active_sessions": len(self.session_metrics),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

# Global enhanced telemetry tracker
telemetry = EnhancedTelemetryTracker()

# Enhanced logging configuration
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
)
logger = logging.getLogger(__name__)

# Enhanced Pydantic Models with validation and documentation
class MCPServerConfig(BaseModel):
    """Enhanced MCP server configuration with validation"""
    name: str = Field(..., description="Unique server name")
    command: str = Field(..., description="Command to execute the server")
    args: List[str] = Field(default_factory=list, description="Command arguments")
    env: Dict[str, str] = Field(default_factory=dict, description="Environment variables")
    description: str = Field(default="", description="Server description")
    enabled: bool = Field(default=True, description="Whether server is enabled")
    transport_type: str = Field(default="stdio", description="Transport protocol (stdio, sse)")
    connection_timeout: float = Field(default=60.0, description="Connection timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum connection retry attempts")

    @field_validator('transport_type')
    @classmethod
    def validate_transport_type(cls, v):
        if v not in ['stdio', 'sse']:
            raise ValueError('transport_type must be either "stdio" or "sse"')
        return v

class ChatMessage(BaseModel):
    """Enhanced chat message model"""
    role: str = Field(..., description="Message role (user, assistant, system)")
    content: str = Field(..., description="Message content")
    timestamp: Optional[str] = Field(default=None, description="Message timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

    @field_validator('role')
    @classmethod
    def validate_role(cls, v):
        if v not in ['user', 'assistant', 'system']:
            raise ValueError('role must be one of: user, assistant, system')
        return v

class ChatRequest(BaseModel):
    """Enhanced chat request model with advanced options"""
    message: str = Field(..., description="User message")
    conversation_id: Optional[str] = Field(default=None, description="Conversation ID for context")
    use_tools: bool = Field(default=True, description="Whether to use available tools")
    tools_filter: Optional[List[str]] = Field(default=None, description="Specific tools to use")
    max_iterations: int = Field(default=10, description="Maximum conversation iterations")
    stream_response: bool = Field(default=False, description="Whether to stream the response")
    include_thinking: bool = Field(default=False, description="Include thinking process in response")
    session_id: Optional[str] = Field(default=None, description="Session ID for enhanced context")

class ChatResponse(BaseModel):
    """Enhanced chat response model with detailed information"""
    response: str = Field(..., description="Assistant response")
    conversation_id: str = Field(..., description="Conversation ID")
    tools_used: List[Dict[str, Any]] = Field(default_factory=list, description="Tools used in response")
    status: str = Field(default="success", description="Response status")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Response metadata")
    execution_time_ms: float = Field(default=0.0, description="Total execution time")
    token_usage: Dict[str, int] = Field(default_factory=dict, description="Token usage statistics")
    session_id: Optional[str] = Field(default=None, description="Session ID")

class ToolExecutionRequest(BaseModel):
    """Model for direct tool execution requests"""
    server_name: str = Field(..., description="Target server name")
    tool_name: str = Field(..., description="Tool name to execute")
    arguments: Dict[str, Any] = Field(default_factory=dict, description="Tool arguments")
    session_id: Optional[str] = Field(default=None, description="Session ID for context")

class ResourceRequest(BaseModel):
    """Model for resource access requests"""
    resource_uri: str = Field(..., description="Resource URI to access")
    server_name: Optional[str] = Field(default=None, description="Specific server name")

class ProgressUpdate(BaseModel):
    """Model for progress updates"""
    token: str = Field(..., description="Progress token")
    progress: float = Field(..., ge=0.0, le=1.0, description="Progress percentage (0.0-1.0)")
    message: str = Field(default="", description="Progress message")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional progress data")

class EnhancedMCPServerConnection:
    """
    Enhanced MCP server connection with latest SDK features including:
    - Multiple transport protocols (stdio, SSE)
    - Progress tracking and monitoring
    - Resource subscriptions
    - Advanced error handling and recovery
    - Comprehensive observability
    """

    def __init__(self, config: MCPServerConfig):
        self.config = config
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.tools = []
        self.resources = []
        self.prompts = []  # New: Prompt support
        self.status = "disconnected"
        self.error = None

        # Enhanced registries with SDK patterns
        self.tool_registry = {}  # name -> {description, input_schema, tool_object}
        self.resource_registry = {}  # uri -> {metadata, resource_object}
        self.prompt_registry = {}  # name -> {description, arguments, prompt_object}

        # Enhanced connection management
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = config.max_retries
        self.reconnect_delay = 5.0
        self.last_heartbeat = None
        self.connection_start_time = None
        self._connection_lock = asyncio.Lock()
        self._read_stream = None
        self._write_stream = None

        # Progress tracking
        self.active_progress_tokens = {}  # token -> {start_time, callback}
        self.progress_callbacks = {}  # token -> callback function

        # Resource subscriptions (for future SDK versions)
        self.resource_subscriptions = set()

        # Performance metrics
        self.connection_metrics = {
            "total_tool_calls": 0,
            "successful_tool_calls": 0,
            "failed_tool_calls": 0,
            "total_resource_reads": 0,
            "connection_uptime": 0,
            "last_activity": None
        }

    async def connect(self):
        """Enhanced connection with multiple transport support and comprehensive error handling"""
        async with self._connection_lock:
            return await self._connect_internal()

    async def _connect_internal(self):
        """Internal connection method with enhanced SDK patterns and transport selection"""
        connection_start = time.time()
        self.connection_start_time = connection_start

        # Reset connection state
        self.exit_stack = AsyncExitStack()
        self.session = None
        self.status = "connecting"
        self.error = None
        self._read_stream = None
        self._write_stream = None

        try:
            logger.info(f"Connecting to MCP server: {self.config.name} using {self.config.transport_type} transport")
            logger.info(f"Command: {self.config.command}")
            logger.info(f"Args: {self.config.args}")
            logger.info(f"Env: {self.config.env}")

            # Enhanced transport selection with SDK patterns
            if self.config.transport_type == "stdio":
                transport = await self._create_stdio_transport()
            elif self.config.transport_type == "sse":
                transport = await self._create_sse_transport()
            else:
                raise ValueError(f"Unsupported transport type: {self.config.transport_type}")

            self._read_stream, self._write_stream = transport
            logger.info(f"Transport created successfully for {self.config.name}")

            # Create enhanced ClientSession with progress tracking
            logger.info(f"Creating enhanced ClientSession for {self.config.name}")
            self.session = await self.exit_stack.enter_async_context(
                ClientSession(self._read_stream, self._write_stream)
            )
            logger.info(f"ClientSession created successfully for {self.config.name}")

            # Initialize session with enhanced timeout and progress tracking
            init_timeout = self.config.connection_timeout
            try:
                self.status = "initializing"

                # Create progress token for initialization
                init_progress_token = ProgressToken(f"init-{self.config.name}-{int(time.time())}")
                self.active_progress_tokens[init_progress_token] = {
                    "start_time": time.time(),
                    "operation": "initialization"
                }

                await asyncio.wait_for(self.session.initialize(), timeout=init_timeout)
                logger.info(f"Session initialized for {self.config.name}")

                # Clean up progress token
                self.active_progress_tokens.pop(init_progress_token, None)

            except asyncio.TimeoutError:
                self.status = "error"
                self.error = f"Session initialization timeout ({init_timeout}s)"
                logger.error(f"Session initialization timeout for {self.config.name}")
                await self._cleanup_connection()

                # Record connection failure
                connection_time = (time.time() - connection_start) * 1000
                telemetry.record_server_connection(self.config.name, "failed", connection_time)
                return False

            # Enhanced capability discovery with comprehensive SDK methods
            try:
                await asyncio.wait_for(self._discover_capabilities_enhanced(), timeout=30.0)
            except asyncio.TimeoutError:
                logger.warning(f"Capability discovery timeout for {self.config.name}, proceeding anyway")

            # Finalize connection
            self.status = "connected"
            self.error = None
            self.reconnect_attempts = 0
            self.last_heartbeat = time.time()
            self.connection_metrics["last_activity"] = time.time()

            # Record successful connection
            connection_time = (time.time() - connection_start) * 1000
            telemetry.record_server_connection(self.config.name, "connected", connection_time)

            logger.info(f"Successfully connected to MCP server: {self.config.name}")
            logger.info(f"  Tools: {len(self.tools)}, Resources: {len(self.resources)}, Prompts: {len(self.prompts)}")
            logger.info(f"  Connection time: {connection_time:.2f}ms")

            return True

        except Exception as e:
            self.status = "error"
            self.error = str(e)
            error_type = type(e).__name__

            logger.error(f"Failed to connect to MCP server {self.config.name}: {e}")
            logger.error(f"Exception type: {error_type}")

            # Record error in telemetry
            telemetry.record_error(f"connection_{error_type}")
            connection_time = (time.time() - connection_start) * 1000
            telemetry.record_server_connection(self.config.name, "failed", connection_time)

            await self._cleanup_connection()
            return False

    async def _create_stdio_transport(self):
        """Create stdio transport with enhanced configuration"""
        server_params = StdioServerParameters(
            command=self.config.command,
            args=self.config.args,
            env=self.config.env
        )
        return await self.exit_stack.enter_async_context(stdio_client(server_params))

    async def _create_sse_transport(self):
        """Create SSE transport for HTTP-based MCP servers"""
        # This would be used for SSE-based MCP servers
        # For now, we'll raise an error as it requires server URL configuration
        raise NotImplementedError("SSE transport requires server URL configuration")

    async def _discover_capabilities_enhanced(self):
        """
        Enhanced capability discovery using latest MCP SDK patterns
        Discovers tools, resources, and prompts with comprehensive metadata
        """
        if not self.session:
            return

        discovery_start = time.time()

        try:
            # Discover tools with enhanced metadata extraction
            await self._discover_tools()

            # Discover resources with enhanced registry building
            await self._discover_resources()

            # Discover prompts (new SDK feature)
            await self._discover_prompts()

            # Log discovery summary
            discovery_time = (time.time() - discovery_start) * 1000
            logger.info(f"Capability discovery completed for {self.config.name} in {discovery_time:.2f}ms")
            logger.info(f"  Discovered: {len(self.tools)} tools, {len(self.resources)} resources, {len(self.prompts)} prompts")

        except Exception as e:
            logger.error(f"Enhanced capability discovery failed for {self.config.name}: {e}")
            telemetry.record_error(f"capability_discovery_{type(e).__name__}")

    async def _discover_tools(self):
        """Discover and register tools with enhanced metadata"""
        try:
            tools_result = await self.session.list_tools()
            self.tools = []
            self.tool_registry = {}

            if hasattr(tools_result, 'tools') and tools_result.tools:
                for tool in tools_result.tools:
                    # Enhanced tool metadata extraction with validation
                    input_schema = self._extract_and_validate_schema(tool)

                    tool_info = {
                        "name": tool.name,
                        "description": getattr(tool, 'description', ''),
                        "input_schema": input_schema,
                        "annotations": getattr(tool, 'annotations', {}),
                        "metadata": {
                            "server": self.config.name,
                            "discovered_at": time.time(),
                            "sdk_version": getattr(tool, '_sdk_version', 'unknown')
                        }
                    }
                    self.tools.append(tool_info)

                    # Build enhanced registry with SDK metadata and validation
                    self.tool_registry[tool.name] = {
                        "description": getattr(tool, 'description', ''),
                        "input_schema": input_schema,
                        "server": self.config.name,
                        "tool_object": tool,
                        "annotations": getattr(tool, 'annotations', {}),
                        "validation_status": "validated" if input_schema else "no_schema"
                    }

                    logger.debug(f"Registered tool: {tool.name} on server {self.config.name}")

        except Exception as e:
            logger.error(f"Tool discovery failed for {self.config.name}: {e}")

    async def _discover_resources(self):
        """Discover and register resources with enhanced metadata"""
        try:
            resources_result = await self.session.list_resources()
            self.resources = []
            self.resource_registry = {}

            if hasattr(resources_result, 'resources') and resources_result.resources:
                for resource in resources_result.resources:
                    resource_info = {
                        "uri": resource.uri,
                        "name": getattr(resource, 'name', ''),
                        "description": getattr(resource, 'description', ''),
                        "mimeType": getattr(resource, 'mimeType', ''),
                        "annotations": getattr(resource, 'annotations', {}),
                        "metadata": {
                            "server": self.config.name,
                            "discovered_at": time.time(),
                            "size_hint": getattr(resource, 'size', None)
                        }
                    }
                    self.resources.append(resource_info)

                    # Build enhanced resource registry with subscription support
                    resource_key = f"{self.config.name}:{resource.uri}"
                    self.resource_registry[resource_key] = {
                        "server": self.config.name,
                        "uri": resource.uri,
                        "name": getattr(resource, 'name', ''),
                        "description": getattr(resource, 'description', ''),
                        "mime_type": getattr(resource, 'mimeType', ''),
                        "annotations": getattr(resource, 'annotations', {}),
                        "resource_object": resource,
                        "subscription_count": 0,
                        "last_accessed": None
                    }

                    logger.debug(f"Registered resource: {resource.uri} on server {self.config.name}")

        except Exception as e:
            logger.error(f"Resource discovery failed for {self.config.name}: {e}")

    async def _discover_prompts(self):
        """Discover and register prompts (new SDK feature)"""
        try:
            prompts_result = await self.session.list_prompts()
            self.prompts = []
            self.prompt_registry = {}

            if hasattr(prompts_result, 'prompts') and prompts_result.prompts:
                for prompt in prompts_result.prompts:
                    prompt_info = {
                        "name": prompt.name,
                        "description": getattr(prompt, 'description', ''),
                        "arguments": getattr(prompt, 'arguments', []),
                        "annotations": getattr(prompt, 'annotations', {}),
                        "metadata": {
                            "server": self.config.name,
                            "discovered_at": time.time()
                        }
                    }
                    self.prompts.append(prompt_info)

                    # Build prompt registry
                    self.prompt_registry[prompt.name] = {
                        "description": getattr(prompt, 'description', ''),
                        "arguments": getattr(prompt, 'arguments', []),
                        "server": self.config.name,
                        "prompt_object": prompt,
                        "annotations": getattr(prompt, 'annotations', {})
                    }

                    logger.debug(f"Registered prompt: {prompt.name} on server {self.config.name}")

        except Exception as e:
            # Prompts might not be supported by all servers
            logger.debug(f"Prompt discovery failed for {self.config.name}: {e}")

    def _extract_and_validate_schema(self, tool) -> Dict[str, Any]:
        """Extract and validate tool input schema with enhanced error handling"""
        try:
            input_schema = {}

            # Try different schema attribute names for compatibility
            if hasattr(tool, 'inputSchema'):
                input_schema = tool.inputSchema
            elif hasattr(tool, 'input_schema'):
                input_schema = tool.input_schema
            elif hasattr(tool, 'schema'):
                input_schema = tool.schema

            # Validate schema structure
            if input_schema and isinstance(input_schema, dict):
                # Ensure basic JSON Schema structure
                if "type" not in input_schema:
                    input_schema["type"] = "object"
                if input_schema.get("type") == "object" and "properties" not in input_schema:
                    input_schema["properties"] = {}

                # Validate with jsonschema if available
                if JSONSCHEMA_AVAILABLE:
                    try:
                        Draft202012Validator.check_schema(input_schema)
                    except Exception as e:
                        logger.warning(f"Schema validation warning for tool {tool.name}: {e}")

            return input_schema

        except Exception as e:
            logger.warning(f"Schema extraction failed for tool {getattr(tool, 'name', 'unknown')}: {e}")
            return {}

    async def ensure_connected(self) -> bool:
        """
        Ensure connection is active with enhanced auto-reconnect and health monitoring
        Uses SDK patterns for robust connection management
        """
        if self.status == "connected" and self.session:
            # Enhanced heartbeat check with multiple validation methods
            try:
                # Quick heartbeat using list_tools (lightweight operation)
                heartbeat_start = time.time()
                await asyncio.wait_for(self.session.list_tools(), timeout=5.0)
                heartbeat_time = (time.time() - heartbeat_start) * 1000

                self.last_heartbeat = time.time()
                self.connection_metrics["last_activity"] = time.time()

                # Log slow heartbeats for monitoring
                if heartbeat_time > 1000:  # > 1 second
                    logger.warning(f"Slow heartbeat for {self.config.name}: {heartbeat_time:.2f}ms")

                return True

            except asyncio.TimeoutError:
                logger.warning(f"Heartbeat timeout for {self.config.name}, attempting reconnect")
                self.status = "disconnected"
                telemetry.record_error("heartbeat_timeout")

            except Exception as e:
                logger.warning(f"Heartbeat failed for {self.config.name}: {e}, attempting reconnect")
                self.status = "disconnected"
                telemetry.record_error(f"heartbeat_{type(e).__name__}")

        if self.status != "connected":
            return await self._attempt_reconnect()

        return False

    async def _attempt_reconnect(self) -> bool:
        """Enhanced reconnection with exponential backoff and comprehensive error handling"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"Max reconnection attempts ({self.max_reconnect_attempts}) reached for {self.config.name}")
            telemetry.record_error("max_reconnect_attempts_reached")
            return False

        self.reconnect_attempts += 1
        delay = min(self.reconnect_delay * (2 ** (self.reconnect_attempts - 1)), 60.0)  # Cap at 60 seconds

        logger.info(f"Reconnecting to {self.config.name} (attempt {self.reconnect_attempts}/{self.max_reconnect_attempts}) in {delay:.1f}s")
        await asyncio.sleep(delay)

        # Clean up previous connection state before reconnecting
        await self._cleanup_connection()

        success = await self._connect_internal()
        if success:
            self.reconnect_attempts = 0
            logger.info(f"Successfully reconnected to {self.config.name}")
            telemetry.record_server_connection(self.config.name, "reconnected")
        else:
            logger.warning(f"Reconnection attempt {self.reconnect_attempts} failed for {self.config.name}")

        return success

    async def _cleanup_connection(self):
        """Enhanced cleanup with comprehensive resource management and error handling"""
        cleanup_start = time.time()

        try:
            # Cancel any active progress tokens
            for token in list(self.active_progress_tokens.keys()):
                self.active_progress_tokens.pop(token, None)

            # Clear resource subscriptions
            self.resource_subscriptions.clear()

            # Safely close the exit stack with shield protection
            if self.exit_stack:
                try:
                    with anyio.CancelScope(shield=True):
                        await asyncio.shield(self.exit_stack.aclose())
                except Exception as e:
                    logger.debug(f"Exit stack cleanup warning for {self.config.name}: {e}")

        except Exception as ce:
            logger.warning(f"Cleanup error for {self.config.name}: {ce}")

        finally:
            # Reset all connection state
            self.session = None
            self._read_stream = None
            self._write_stream = None
            self.exit_stack = AsyncExitStack()

            cleanup_time = (time.time() - cleanup_start) * 1000
            logger.debug(f"Connection cleanup completed for {self.config.name} in {cleanup_time:.2f}ms")

    def validate_tool_arguments_enhanced(self, tool_name: str, arguments: Dict[str, Any]) -> tuple[Dict[str, Any], List[str], bool]:
        """
        Enhanced tool input validation using latest MCP schema patterns
        Returns: (validated_arguments, warnings, is_valid)
        """
        warnings = []
        is_valid = True

        # Get tool info from enhanced registry with O(1) lookup
        tool_info = self.tool_registry.get(tool_name)
        if not tool_info:
            warnings.append(f"Tool {tool_name} not found in registry")
            return arguments, warnings, False

        schema = tool_info.get("input_schema", {})
        if not schema:
            # No schema means no validation required
            return arguments, warnings, True

        try:
            # Enhanced validation with comprehensive error handling
            if JSONSCHEMA_AVAILABLE:
                validated_args, schema_warnings, schema_valid = self._validate_with_jsonschema(arguments, schema, tool_name)
                warnings.extend(schema_warnings)
                is_valid = schema_valid

                # Apply any schema-based transformations
                if is_valid:
                    arguments = self._apply_schema_transformations(validated_args, schema)

            else:
                # Enhanced fallback validation with better error reporting
                validated_args, fallback_warnings, fallback_valid = self._enhanced_fallback_validation(arguments, schema, tool_name)
                warnings.extend(fallback_warnings)
                is_valid = fallback_valid
                arguments = validated_args

            # Additional MCP-specific validations
            mcp_warnings = self._validate_mcp_specific_patterns(arguments, schema, tool_name)
            warnings.extend(mcp_warnings)

            return arguments, warnings, is_valid

        except Exception as e:
            error_msg = f"Schema validation error: {str(e)}"
            warnings.append(error_msg)
            logger.error(f"Validation exception for tool {tool_name}: {e}")
            telemetry.record_error(f"validation_{type(e).__name__}")
            return arguments, warnings, False

    def _validate_with_jsonschema(self, arguments: Dict[str, Any], schema: Dict, tool_name: str) -> tuple[Dict[str, Any], List[str], bool]:
        """Validate using jsonschema with enhanced error reporting"""
        warnings = []

        try:
            # Use Draft 2020-12 validator for latest JSON Schema support
            validator = Draft202012Validator(schema)
            errors = list(validator.iter_errors(arguments))

            if errors:
                error_details = []
                for error in errors[:5]:  # Limit to first 5 errors for readability
                    error_path = '.'.join(str(p) for p in error.path) if error.path else 'root'
                    error_details.append(f"Path: {error_path}, Error: {error.message}")

                warnings.append(f"Schema validation failed: {'; '.join(error_details)}")
                return arguments, warnings, False

            # Validation successful
            return arguments, warnings, True

        except Exception as e:
            warnings.append(f"JSON Schema validation error: {str(e)}")
            return arguments, warnings, False

    def _enhanced_fallback_validation(self, arguments: Dict[str, Any], schema: Dict, tool_name: str) -> tuple[Dict[str, Any], List[str], bool]:
        """Enhanced fallback validation with comprehensive checks"""
        warnings = []
        is_valid = True

        # Check required fields with detailed error messages
        if "required" in schema:
            missing_fields = []
            for req_field in schema["required"]:
                if req_field not in arguments:
                    missing_fields.append(req_field)
                    is_valid = False

            if missing_fields:
                warnings.append(f"Missing required fields: {', '.join(missing_fields)}")

        # Enhanced type checking for properties
        if "properties" in schema:
            for prop_name, prop_schema in schema["properties"].items():
                if prop_name in arguments:
                    value = arguments[prop_name]
                    expected_type = prop_schema.get("type")

                    if expected_type:
                        type_valid, type_warning = self._validate_type_enhanced(value, expected_type, prop_name)
                        if not type_valid:
                            warnings.append(type_warning)
                            is_valid = False

                    # Check additional constraints
                    constraint_warnings = self._check_value_constraints(value, prop_schema, prop_name)
                    warnings.extend(constraint_warnings)

        return arguments, warnings, is_valid

    def _validate_type_enhanced(self, value: Any, expected_type: str, field_name: str) -> tuple[bool, str]:
        """Enhanced type validation with better error messages"""
        type_map = {
            "string": str,
            "number": (int, float),
            "integer": int,
            "boolean": bool,
            "array": list,
            "object": dict,
            "null": type(None)
        }

        expected_python_type = type_map.get(expected_type)
        if expected_python_type:
            if isinstance(value, expected_python_type):
                return True, ""
            else:
                actual_type = type(value).__name__
                return False, f"Field '{field_name}' has type {actual_type}, expected {expected_type}"

        # Unknown type, log warning but assume valid
        logger.warning(f"Unknown type '{expected_type}' for field {field_name}, assuming valid")
        return True, ""

    def _check_value_constraints(self, value: Any, prop_schema: Dict, field_name: str) -> List[str]:
        """Check additional value constraints like min/max, pattern, etc."""
        warnings = []

        try:
            # Check string constraints
            if isinstance(value, str):
                if "minLength" in prop_schema and len(value) < prop_schema["minLength"]:
                    warnings.append(f"Field '{field_name}' is too short (min: {prop_schema['minLength']})")
                if "maxLength" in prop_schema and len(value) > prop_schema["maxLength"]:
                    warnings.append(f"Field '{field_name}' is too long (max: {prop_schema['maxLength']})")

            # Check numeric constraints
            elif isinstance(value, (int, float)):
                if "minimum" in prop_schema and value < prop_schema["minimum"]:
                    warnings.append(f"Field '{field_name}' is below minimum ({prop_schema['minimum']})")
                if "maximum" in prop_schema and value > prop_schema["maximum"]:
                    warnings.append(f"Field '{field_name}' is above maximum ({prop_schema['maximum']})")

            # Check array constraints
            elif isinstance(value, list):
                if "minItems" in prop_schema and len(value) < prop_schema["minItems"]:
                    warnings.append(f"Field '{field_name}' has too few items (min: {prop_schema['minItems']})")
                if "maxItems" in prop_schema and len(value) > prop_schema["maxItems"]:
                    warnings.append(f"Field '{field_name}' has too many items (max: {prop_schema['maxItems']})")

        except Exception as e:
            logger.debug(f"Constraint checking error for field {field_name}: {e}")

        return warnings

    def _apply_schema_transformations(self, arguments: Dict[str, Any], schema: Dict) -> Dict[str, Any]:
        """Apply schema-based transformations like default values"""
        try:
            if "properties" in schema:
                for prop_name, prop_schema in schema["properties"].items():
                    # Apply default values for missing optional properties
                    if prop_name not in arguments and "default" in prop_schema:
                        arguments[prop_name] = prop_schema["default"]

        except Exception as e:
            logger.debug(f"Schema transformation error: {e}")

        return arguments

    def _validate_mcp_specific_patterns(self, arguments: Dict[str, Any], schema: Dict, tool_name: str) -> List[str]:
        """Validate MCP-specific patterns and conventions"""
        warnings = []

        try:
            # Check for MCP-specific annotations
            if "mcp_metadata" in schema:
                mcp_meta = schema["mcp_metadata"]
                if mcp_meta.get("validation_level") == "strict":
                    # Apply stricter validation for MCP tools
                    pass

            # Validate resource URIs if present
            for key, value in arguments.items():
                if isinstance(value, str) and (key.endswith("_uri") or key.endswith("_url")):
                    if not self._is_valid_uri_format(value):
                        warnings.append(f"Field '{key}' does not appear to be a valid URI format")

        except Exception as e:
            logger.debug(f"MCP-specific validation error for tool {tool_name}: {e}")

        return warnings

    def _is_valid_uri_format(self, uri: str) -> bool:
        """Basic URI format validation"""
        try:
            from urllib.parse import urlparse
            result = urlparse(uri)
            return bool(result.scheme or result.path)
        except Exception:
            return True  # If validation fails, assume valid

    async def call_tool_enhanced(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        progress_callback: Optional[Callable] = None,
        timeout: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Enhanced tool execution with latest MCP SDK features including:
        - Progress tracking and monitoring
        - Comprehensive validation and error handling
        - Performance metrics and observability
        - Timeout management
        - Connection health verification
        """
        execution_start = time.time()
        progress_token = None

        try:
            # Ensure connection is healthy with enhanced verification
            if not await self.ensure_connected():
                error_msg = f"Server {self.config.name} is not connected and reconnection failed"
                if self.error:
                    error_msg += f": {self.error}"

                return {
                    "success": False,
                    "error": error_msg,
                    "tool_name": tool_name,
                    "server_name": self.config.name,
                    "error_type": "connection_failed",
                    "execution_time": (time.time() - execution_start) * 1000
                }

            # Enhanced tool existence check with detailed error reporting
            if tool_name not in self.tool_registry:
                available_tools = list(self.tool_registry.keys())
                similar_tools = [t for t in available_tools if tool_name.lower() in t.lower()]

                error_msg = f"Tool '{tool_name}' not found on server {self.config.name}"
                if similar_tools:
                    error_msg += f". Similar tools: {similar_tools[:3]}"
                else:
                    error_msg += f". Available tools: {available_tools[:5]}"

                return {
                    "success": False,
                    "error": error_msg,
                    "tool_name": tool_name,
                    "server_name": self.config.name,
                    "available_tools": available_tools,
                    "error_type": "tool_not_found",
                    "execution_time": (time.time() - execution_start) * 1000
                }

            # Enhanced argument validation with comprehensive error reporting
            validated_args, validation_warnings, is_valid = self.validate_tool_arguments_enhanced(tool_name, arguments)

            if not is_valid:
                critical_errors = [w for w in validation_warnings if any(
                    keyword in w for keyword in ["Missing required field", "Schema validation failed", "has incorrect type"]
                )]

                return {
                    "success": False,
                    "error": f"Validation failed: {'; '.join(critical_errors)}",
                    "tool_name": tool_name,
                    "server_name": self.config.name,
                    "validation_warnings": validation_warnings,
                    "error_type": "validation_failed",
                    "execution_time": (time.time() - execution_start) * 1000
                }

            # Log validation warnings but continue execution
            if validation_warnings:
                logger.warning(f"Tool {tool_name} validation warnings: {validation_warnings}")

            # Create progress token if callback provided
            if progress_callback:
                progress_token = ProgressToken(f"tool-{tool_name}-{int(time.time() * 1000)}")
                self.active_progress_tokens[progress_token] = {
                    "start_time": time.time(),
                    "callback": progress_callback,
                    "operation": f"tool_execution_{tool_name}"
                }
                telemetry.record_progress_event()

            logger.info(f"Executing tool {tool_name} on {self.config.name} with validated arguments")
            logger.debug(f"Tool arguments: {validated_args}")

            # Enhanced tool execution with timeout and progress tracking
            tool_start_time = time.time()

            try:
                # Set default timeout if not provided
                execution_timeout = timeout or 30.0

                # Execute tool with SDK patterns and timeout
                if progress_token and progress_callback:
                    # Simulate progress updates during execution
                    progress_task = asyncio.create_task(
                        self._track_tool_progress(progress_token, progress_callback, execution_timeout)
                    )

                result = await asyncio.wait_for(
                    self.session.call_tool(tool_name, validated_args),
                    timeout=execution_timeout
                )

                if progress_token and progress_callback:
                    progress_task.cancel()
                    try:
                        await progress_task
                    except asyncio.CancelledError:
                        pass

            except asyncio.TimeoutError:
                if progress_token:
                    self.active_progress_tokens.pop(progress_token, None)

                return {
                    "success": False,
                    "error": f"Tool execution timeout after {execution_timeout}s",
                    "tool_name": tool_name,
                    "server_name": self.config.name,
                    "error_type": "execution_timeout",
                    "execution_time": (time.time() - execution_start) * 1000
                }

            execution_time_ms = (time.time() - tool_start_time) * 1000
            total_time_ms = (time.time() - execution_start) * 1000

            # Clean up progress token
            if progress_token:
                self.active_progress_tokens.pop(progress_token, None)
                if progress_callback:
                    await progress_callback(progress_token, 1.0, "Tool execution completed")

            # Enhanced result processing with comprehensive content extraction
            processed_result = self._process_tool_result(result, tool_name)

            # Update connection metrics
            self.connection_metrics["total_tool_calls"] += 1
            self.connection_metrics["successful_tool_calls"] += 1
            self.connection_metrics["last_activity"] = time.time()

            # Record telemetry
            telemetry.record_tool_execution(execution_time_ms, self.config.name, tool_name)

            logger.info(f"Tool {tool_name} executed successfully in {execution_time_ms:.2f}ms")

            return {
                "success": True,
                "result": processed_result["content"],
                "tool_name": tool_name,
                "server_name": self.config.name,
                "execution_time": total_time_ms,
                "tool_execution_time": execution_time_ms,
                "metadata": processed_result["metadata"],
                "validation_warnings": validation_warnings if validation_warnings else [],
                "content_type": processed_result["content_type"]
            }

        except Exception as e:
            # Clean up progress token on error
            if progress_token:
                self.active_progress_tokens.pop(progress_token, None)

            execution_time_ms = (time.time() - execution_start) * 1000
            error_type = type(e).__name__

            # Update connection metrics
            self.connection_metrics["total_tool_calls"] += 1
            self.connection_metrics["failed_tool_calls"] += 1

            logger.error(f"Enhanced tool execution error for {tool_name} on {self.config.name}: {e}")

            # Enhanced error categorization and handling
            if any(keyword in str(e).lower() for keyword in ["connection", "transport", "broken pipe", "eof"]):
                self.status = "disconnected"
                logger.warning(f"Connection error detected for {self.config.name}, marking for reconnection")
                error_type = "connection_error"
                telemetry.record_error("tool_connection_error")
            else:
                telemetry.record_error(f"tool_{error_type}")

            return {
                "success": False,
                "error": f"Tool execution failed: {str(e)}",
                "tool_name": tool_name,
                "server_name": self.config.name,
                "error_type": error_type,
                "execution_time": execution_time_ms,
                "exception_details": {
                    "type": error_type,
                    "message": str(e),
                    "args": getattr(e, 'args', [])
                }
            }
    async def _track_tool_progress(self, progress_token: ProgressToken, callback: Callable, timeout: float):
        """Track tool execution progress with periodic updates"""
        try:
            start_time = time.time()

            while time.time() - start_time < timeout:
                elapsed = time.time() - start_time
                progress = min(elapsed / timeout, 0.9)  # Cap at 90% until completion

                await callback(progress_token, progress, f"Executing tool... ({elapsed:.1f}s)")
                await asyncio.sleep(0.5)  # Update every 500ms

        except asyncio.CancelledError:
            # Tool completed, this is expected
            pass
        except Exception as e:
            logger.debug(f"Progress tracking error: {e}")

    def _process_tool_result(self, result, tool_name: str) -> Dict[str, Any]:
        """
        Enhanced result processing with comprehensive content extraction
        Handles various MCP result formats and content types
        """
        try:
            processed = {
                "content": "",
                "content_type": "text",
                "metadata": {}
            }

            # Extract metadata if available
            if hasattr(result, 'meta'):
                processed["metadata"] = result.meta
            elif hasattr(result, '_meta'):
                processed["metadata"] = result._meta

            # Enhanced content extraction with support for multiple content types
            if hasattr(result, 'content'):
                content = result.content

                if isinstance(content, list):
                    # Handle list of content items (MCP standard format)
                    text_parts = []
                    images = []
                    other_content = []

                    for item in content:
                        if hasattr(item, 'text'):
                            text_parts.append(item.text)
                        elif hasattr(item, 'image'):
                            images.append(item.image)
                            processed["content_type"] = "mixed"
                        elif isinstance(item, dict):
                            if 'text' in item:
                                text_parts.append(item['text'])
                            elif 'image' in item:
                                images.append(item['image'])
                                processed["content_type"] = "mixed"
                            elif 'json' in item:
                                other_content.append(json.dumps(item['json'], indent=2))
                                processed["content_type"] = "json"
                            else:
                                other_content.append(str(item))
                        else:
                            other_content.append(str(item))

                    # Combine all content
                    all_content = text_parts + other_content
                    processed["content"] = '\n'.join(all_content) if all_content else ""

                    # Add image information to metadata
                    if images:
                        processed["metadata"]["images"] = len(images)

                elif isinstance(content, str):
                    processed["content"] = content

                elif isinstance(content, dict):
                    # Handle structured content
                    processed["content"] = json.dumps(content, indent=2)
                    processed["content_type"] = "json"

                else:
                    processed["content"] = str(content)

            elif hasattr(result, 'text'):
                # Direct text result
                processed["content"] = result.text

            elif hasattr(result, 'data'):
                # Data result
                processed["content"] = str(result.data)
                processed["content_type"] = "data"

            else:
                # Fallback to string representation
                processed["content"] = str(result)

            # Ensure content is not empty
            if not processed["content"]:
                processed["content"] = f"Tool {tool_name} executed successfully (no content returned)"

            return processed

        except Exception as e:
            logger.error(f"Result processing error for tool {tool_name}: {e}")
            return {
                "content": f"Tool {tool_name} executed but result processing failed: {str(e)}",
                "content_type": "error",
                "metadata": {"processing_error": str(e)}
            }

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Legacy method - redirects to enhanced tool execution"""
        return await self.call_tool_enhanced(tool_name, arguments)

    async def read_resource_enhanced(
        self,
        resource_uri: str,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Enhanced resource reading with progress tracking and comprehensive error handling
        """
        read_start = time.time()
        progress_token = None

        try:
            # Ensure connection is healthy
            if not await self.ensure_connected():
                return {
                    "success": False,
                    "error": f"Server {self.config.name} is not connected",
                    "resource_uri": resource_uri,
                    "server_name": self.config.name
                }

            # Check if resource exists in registry
            resource_key = f"{self.config.name}:{resource_uri}"
            if resource_key not in self.resource_registry:
                available_resources = [info["uri"] for info in self.resource_registry.values()]
                return {
                    "success": False,
                    "error": f"Resource {resource_uri} not found on server {self.config.name}",
                    "available_resources": available_resources[:10],  # Limit to first 10
                    "resource_uri": resource_uri,
                    "server_name": self.config.name
                }

            # Create progress token if callback provided
            if progress_callback:
                progress_token = ProgressToken(f"resource-{resource_uri}-{int(time.time() * 1000)}")
                self.active_progress_tokens[progress_token] = {
                    "start_time": time.time(),
                    "callback": progress_callback,
                    "operation": f"resource_read_{resource_uri}"
                }
                await progress_callback(progress_token, 0.1, "Starting resource read...")

            # Read resource using SDK methods
            from pydantic import AnyUrl
            result = await self.session.read_resource(AnyUrl(resource_uri))

            # Update progress
            if progress_callback and progress_token:
                await progress_callback(progress_token, 0.8, "Processing resource content...")

            # Process resource content
            processed_content = self._process_resource_content(result, resource_uri)

            # Update metrics
            self.connection_metrics["total_resource_reads"] += 1
            self.resource_registry[resource_key]["last_accessed"] = time.time()
            telemetry.record_resource_read()

            # Final progress update
            if progress_callback and progress_token:
                await progress_callback(progress_token, 1.0, "Resource read completed")
                self.active_progress_tokens.pop(progress_token, None)

            read_time = (time.time() - read_start) * 1000
            logger.info(f"Resource {resource_uri} read successfully in {read_time:.2f}ms")

            return {
                "success": True,
                "uri": resource_uri,
                "content": processed_content["content"],
                "content_type": processed_content["content_type"],
                "server": self.config.name,
                "metadata": processed_content["metadata"],
                "read_time": read_time
            }

        except Exception as e:
            if progress_token:
                self.active_progress_tokens.pop(progress_token, None)

            read_time = (time.time() - read_start) * 1000
            logger.error(f"Failed to read resource {resource_uri}: {e}")
            telemetry.record_error(f"resource_read_{type(e).__name__}")

            return {
                "success": False,
                "error": f"Resource read failed: {str(e)}",
                "resource_uri": resource_uri,
                "server_name": self.config.name,
                "error_type": type(e).__name__,
                "read_time": read_time
            }

    def _process_resource_content(self, result, resource_uri: str) -> Dict[str, Any]:
        """Process resource content with support for various content types"""
        try:
            processed = {
                "content": "",
                "content_type": "text",
                "metadata": {}
            }

            if hasattr(result, 'contents') and result.contents:
                # Handle multiple content items
                content_parts = []

                for content_item in result.contents:
                    if hasattr(content_item, 'text'):
                        content_parts.append(content_item.text)
                    elif hasattr(content_item, 'blob'):
                        # Handle binary content
                        blob_data = content_item.blob
                        if isinstance(blob_data, bytes):
                            try:
                                # Try to decode as UTF-8 text
                                content_parts.append(blob_data.decode('utf-8'))
                            except UnicodeDecodeError:
                                # Handle as binary data
                                processed["content_type"] = "binary"
                                processed["metadata"]["blob_size"] = len(blob_data)
                                content_parts.append(f"<Binary data: {len(blob_data)} bytes>")
                        else:
                            content_parts.append(str(blob_data))
                    else:
                        content_parts.append(str(content_item))

                processed["content"] = '\n'.join(content_parts)

            elif hasattr(result, 'content'):
                processed["content"] = str(result.content)

            else:
                processed["content"] = str(result)

            # Extract additional metadata
            if hasattr(result, 'mimeType'):
                processed["metadata"]["mime_type"] = result.mimeType
            if hasattr(result, 'annotations'):
                processed["metadata"]["annotations"] = result.annotations

            return processed

        except Exception as e:
            logger.error(f"Resource content processing error for {resource_uri}: {e}")
            return {
                "content": f"Resource content processing failed: {str(e)}",
                "content_type": "error",
                "metadata": {"processing_error": str(e)}
            }

    async def disconnect(self):
        """Enhanced disconnect with comprehensive cleanup and metrics"""
        disconnect_start = time.time()

        try:
            logger.info(f"Starting enhanced disconnect for {self.config.name}")

            # Calculate uptime
            if self.connection_start_time:
                uptime = time.time() - self.connection_start_time
                self.connection_metrics["connection_uptime"] = uptime
                logger.info(f"Connection uptime: {uptime:.2f}s")

            # Log connection statistics
            logger.info(f"Connection statistics for {self.config.name}:")
            logger.info(f"  Total tool calls: {self.connection_metrics['total_tool_calls']}")
            logger.info(f"  Successful: {self.connection_metrics['successful_tool_calls']}")
            logger.info(f"  Failed: {self.connection_metrics['failed_tool_calls']}")
            logger.info(f"  Resource reads: {self.connection_metrics['total_resource_reads']}")

            # Perform comprehensive cleanup
            await self._cleanup_connection()

            self.status = "disconnected"
            disconnect_time = (time.time() - disconnect_start) * 1000
            logger.info(f"Successfully disconnected from {self.config.name} in {disconnect_time:.2f}ms")

        except Exception as e:
            logger.error(f"Error during enhanced disconnect from {self.config.name}: {e}")
            # Force cleanup even if there were errors
            self.session = None
            self.status = "error"


class EnhancedMCPClientManager:
    """
    Enhanced MCP Client Manager with latest SDK features including:
    - Multiple transport protocols support
    - Advanced connection management and health monitoring
    - Resource subscriptions and real-time updates
    - Comprehensive observability and metrics
    - Session-aware operations
    - Progress tracking and streaming
    """

    def __init__(self):
        self.connections: Dict[str, EnhancedMCPServerConnection] = {}
        self.bedrock_client = None
        self.async_bedrock_client = None  # For async operations

        # Enhanced registries with comprehensive metadata
        self.global_tool_registry = {}  # Global registry: tool_key -> {server, tool_info, metadata}
        self.global_resource_registry = {}  # Enhanced resource registry: resource_key -> {server, metadata}
        self.global_prompt_registry = {}  # Prompt registry: prompt_key -> {server, prompt_info}

        # Advanced features
        self.resource_subscriptions = {}  # Resource subscriptions: uri -> set of subscribers
        self.active_sessions = {}  # Active session tracking: session_id -> session_info
        self.connection_pool_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "failed_connections": 0,
            "total_reconnections": 0
        }

        # Initialize clients
        self._initialize_bedrock()
        self._initialize_async_bedrock()

    def _initialize_bedrock(self):
        """Initialize AWS Bedrock client with enhanced configuration"""
        try:
            # Enhanced configuration with better retry and timeout settings
            config = Config(
                region_name=os.getenv('AWS_REGION', 'ap-south-1'),
                retries={
                    'max_attempts': 3,
                    'mode': 'adaptive',
                    'total_max_attempts': 5
                },
                read_timeout=60,
                connect_timeout=10,
                max_pool_connections=50
            )

            # Initialize synchronous client
            self.bedrock_client = boto3.client(
                'bedrock-runtime',
                config=config,
                aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
            )

            logger.info("Enhanced Bedrock client initialized successfully")
            logger.info(f"Region: {config.region_name}")

        except Exception as e:
            logger.error(f"Failed to initialize Bedrock client: {e}")
            raise

    def _initialize_async_bedrock(self):
        """Initialize async AWS Bedrock client for enhanced operations"""
        try:
            if AIOBOTO3_AVAILABLE:
                # Async client will be created on-demand to avoid session issues
                logger.info("Async Bedrock client support available")
            else:
                logger.warning("aioboto3 not available, async operations will use sync client")

        except Exception as e:
            logger.error(f"Failed to initialize async Bedrock client: {e}")

    async def get_async_bedrock_client(self):
        """Get or create async Bedrock client on-demand"""
        if AIOBOTO3_AVAILABLE:
            try:
                import aioboto3
                session = aioboto3.Session()

                config = Config(
                    region_name=os.getenv('AWS_REGION', 'ap-south-1'),
                    retries={
                        'max_attempts': 3,
                        'mode': 'adaptive'
                    }
                )

                client = session.client(
                    'bedrock-runtime',
                    config=config,
                    aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                    aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
                )

                return await client.__aenter__()

            except Exception as e:
                logger.error(f"Failed to create async Bedrock client: {e}")
                return None
        else:
            return None

    def _rebuild_global_registries(self):
        """
        Enhanced global registry building with comprehensive metadata and conflict resolution
        Rebuilds tool, resource, and prompt registries from all connected servers
        """
        registry_start = time.time()

        # Clear existing registries
        self.global_tool_registry = {}
        self.global_resource_registry = {}
        self.global_prompt_registry = {}

        connected_servers = 0
        total_tools = 0
        total_resources = 0
        total_prompts = 0

        for server_name, connection in self.connections.items():
            if connection.status == "connected":
                connected_servers += 1

                # Rebuild enhanced tool registry with conflict resolution
                for tool_name, tool_info in connection.tool_registry.items():
                    # Create unique global key to handle name conflicts
                    global_key = f"{server_name}::{tool_name}"

                    self.global_tool_registry[global_key] = {
                        "server": server_name,
                        "tool_name": tool_name,
                        "description": tool_info["description"],
                        "input_schema": tool_info["input_schema"],
                        "annotations": tool_info.get("annotations", {}),
                        "validation_status": tool_info.get("validation_status", "unknown"),
                        "metadata": {
                            "registered_at": time.time(),
                            "server_type": connection.config.transport_type,
                            "tool_object": tool_info.get("tool_object")
                        }
                    }
                    total_tools += 1

                # Rebuild enhanced resource registry
                for resource_key, resource_info in connection.resource_registry.items():
                    enhanced_resource_info = {
                        **resource_info,
                        "metadata": {
                            **resource_info.get("metadata", {}),
                            "registered_at": time.time(),
                            "server_type": connection.config.transport_type,
                            "subscription_count": resource_info.get("subscription_count", 0)
                        }
                    }
                    self.global_resource_registry[resource_key] = enhanced_resource_info
                    total_resources += 1

                # Rebuild prompt registry (new feature)
                for prompt_name, prompt_info in connection.prompt_registry.items():
                    prompt_key = f"{server_name}::{prompt_name}"

                    self.global_prompt_registry[prompt_key] = {
                        "server": server_name,
                        "prompt_name": prompt_name,
                        "description": prompt_info["description"],
                        "arguments": prompt_info["arguments"],
                        "annotations": prompt_info.get("annotations", {}),
                        "metadata": {
                            "registered_at": time.time(),
                            "server_type": connection.config.transport_type,
                            "prompt_object": prompt_info.get("prompt_object")
                        }
                    }
                    total_prompts += 1

        registry_time = (time.time() - registry_start) * 1000

        logger.info(f"Global registries rebuilt in {registry_time:.2f}ms:")
        logger.info(f"  Connected servers: {connected_servers}")
        logger.info(f"  Tools: {total_tools}")
        logger.info(f"  Resources: {total_resources}")
        logger.info(f"  Prompts: {total_prompts}")

        # Update connection pool stats
        self.connection_pool_stats["active_connections"] = connected_servers

    async def add_server(self, config: MCPServerConfig) -> Dict[str, Any]:
        """
        Enhanced server addition with comprehensive error handling and metrics
        Returns detailed connection result with status and metadata
        """
        add_start = time.time()

        try:
            logger.info(f"Adding MCP server: {config.name}")

            # Validate configuration
            validation_result = self._validate_server_config(config)
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "server_name": config.name,
                    "error": f"Configuration validation failed: {validation_result['error']}",
                    "error_type": "config_validation_failed"
                }

            # Check for duplicate server names
            if config.name in self.connections:
                logger.warning(f"Server {config.name} already exists, disconnecting old connection")
                await self.remove_server(config.name)

            # Create enhanced connection object
            connection = EnhancedMCPServerConnection(config)
            self.connections[config.name] = connection

            # Update connection pool stats
            self.connection_pool_stats["total_connections"] += 1

            # Attempt to connect with timeout
            connection_timeout = config.connection_timeout + 10.0  # Add buffer
            try:
                success = await asyncio.wait_for(connection.connect(), timeout=connection_timeout)
            except asyncio.TimeoutError:
                success = False
                connection.error = f"Connection timeout after {connection_timeout}s"
                logger.error(f"Connection timeout for server {config.name}")

            add_time = (time.time() - add_start) * 1000

            if success:
                # Rebuild global registries if connection successful
                self._rebuild_global_registries()

                logger.info(f"Successfully added server {config.name} in {add_time:.2f}ms")

                return {
                    "success": True,
                    "server_name": config.name,
                    "connection_time": add_time,
                    "tools_count": len(connection.tools),
                    "resources_count": len(connection.resources),
                    "prompts_count": len(connection.prompts),
                    "transport_type": config.transport_type,
                    "status": connection.status
                }
            else:
                # Connection failed, clean up
                self.connections.pop(config.name, None)
                self.connection_pool_stats["failed_connections"] += 1

                error_msg = connection.error or "Unknown connection error"
                logger.error(f"Failed to add server {config.name}: {error_msg}")

                return {
                    "success": False,
                    "server_name": config.name,
                    "error": error_msg,
                    "connection_time": add_time,
                    "error_type": "connection_failed",
                    "status": connection.status
                }

        except Exception as e:
            add_time = (time.time() - add_start) * 1000
            error_type = type(e).__name__

            # Clean up on exception
            self.connections.pop(config.name, None)
            self.connection_pool_stats["failed_connections"] += 1

            logger.error(f"Exception while adding server {config.name}: {e}")
            telemetry.record_error(f"add_server_{error_type}")

            return {
                "success": False,
                "server_name": config.name,
                "error": f"Server addition failed: {str(e)}",
                "connection_time": add_time,
                "error_type": error_type,
                "exception_details": str(e)
            }

    def _validate_server_config(self, config: MCPServerConfig) -> Dict[str, Any]:
        """Validate server configuration with comprehensive checks"""
        try:
            # Basic validation
            if not config.name or not config.name.strip():
                return {"valid": False, "error": "Server name cannot be empty"}

            if not config.command or not config.command.strip():
                return {"valid": False, "error": "Server command cannot be empty"}

            # Validate transport type
            if config.transport_type not in ["stdio", "sse"]:
                return {"valid": False, "error": f"Invalid transport type: {config.transport_type}"}

            # Validate timeout values
            if config.connection_timeout <= 0:
                return {"valid": False, "error": "Connection timeout must be positive"}

            if config.max_retries < 0:
                return {"valid": False, "error": "Max retries cannot be negative"}

            # Additional validation for SSE transport
            if config.transport_type == "sse":
                # SSE requires URL configuration (would be added to config in future)
                return {"valid": False, "error": "SSE transport not fully implemented"}

            return {"valid": True, "error": None}

        except Exception as e:
            return {"valid": False, "error": f"Configuration validation error: {str(e)}"}

    async def remove_server(self, server_name: str) -> Dict[str, Any]:
        """
        Enhanced server removal with comprehensive cleanup and metrics
        """
        remove_start = time.time()

        try:
            if server_name not in self.connections:
                return {
                    "success": False,
                    "server_name": server_name,
                    "error": "Server not found",
                    "error_type": "server_not_found"
                }

            connection = self.connections[server_name]

            logger.info(f"Removing server: {server_name}")

            # Disconnect and cleanup
            await connection.disconnect()

            # Remove from connections
            del self.connections[server_name]

            # Clean up any resource subscriptions for this server
            subscriptions_removed = 0
            for uri in list(self.resource_subscriptions.keys()):
                if uri.startswith(f"{server_name}:"):
                    del self.resource_subscriptions[uri]
                    subscriptions_removed += 1

            # Rebuild global registries
            self._rebuild_global_registries()

            remove_time = (time.time() - remove_start) * 1000

            logger.info(f"Successfully removed server {server_name} in {remove_time:.2f}ms")

            return {
                "success": True,
                "server_name": server_name,
                "remove_time": remove_time,
                "subscriptions_removed": subscriptions_removed
            }

        except Exception as e:
            remove_time = (time.time() - remove_start) * 1000
            logger.error(f"Error removing server {server_name}: {e}")

            return {
                "success": False,
                "server_name": server_name,
                "error": f"Server removal failed: {str(e)}",
                "remove_time": remove_time,
                "error_type": type(e).__name__
            }

    def get_available_tools(self) -> Dict[str, Dict[str, Any]]:
        """Get all available tools using optimized global registry"""
        tools = {}
        for tool_key, tool_info in self.global_tool_registry.items():
            server_name = tool_info["server"]
            connection = self.connections.get(server_name)

            # Only include tools from connected servers
            if connection and connection.status == "connected":
                tools[tool_key] = {
                    "server": server_name,
                    "tool": {
                        "name": tool_info["tool_name"],
                        "description": tool_info["description"],
                        "input_schema": tool_info["input_schema"]
                    },
                    "connection": connection
                }
        return tools

    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced tool calling with automatic reconnection"""
        if server_name not in self.connections:
            return {
                "success": False,
                "error": f"Server {server_name} not found",
                "tool_name": tool_name,
                "server_name": server_name
            }

        connection = self.connections[server_name]

        # Use enhanced connection management
        if not await connection.ensure_connected():
            return {
                "success": False,
                "error": f"Server {server_name} is not connected and reconnection failed: {connection.error}",
                "tool_name": tool_name,
                "server_name": server_name
            }

        return await connection.call_tool(tool_name, arguments)

    async def read_resource(self, resource_uri: str, server_name: str = None) -> Optional[Dict]:
        """Read resource content using MCP SDK methods"""
        connection = None

        if server_name:
            connection = self.connections.get(server_name)
            if not connection:
                logger.error(f"Server {server_name} not found")
                return None
        else:
            # Find server by resource URI
            for resource_key, resource_info in self.global_resource_registry.items():
                if resource_info["uri"] == resource_uri:
                    server_name = resource_info["server"]
                    connection = self.connections.get(server_name)
                    break

        if not connection:
            logger.error(f"No server found for resource {resource_uri}")
            return None

        # Ensure connection is healthy
        if not await connection.ensure_connected():
            logger.error(f"Failed to connect to server {connection.config.name}")
            return None

        try:
            from pydantic import AnyUrl
            result = await connection.session.read_resource(AnyUrl(resource_uri))
            return {
                "uri": resource_uri,
                "content": result.contents,
                "server": connection.config.name,
                "metadata": {
                    "mime_type": getattr(result, 'mimeType', None),
                    "annotations": getattr(result, 'annotations', {})
                }
            }
        except Exception as e:
            logger.error(f"Failed to read resource {resource_uri}: {e}")
            return None

    def get_available_resources(self) -> Dict[str, Dict]:
        """Get all available resources from connected servers"""
        return self.global_resource_registry.copy()

    def get_available_prompts(self) -> Dict[str, Dict]:
        """Get all available prompts with enhanced metadata"""
        return self.global_prompt_registry.copy()

    async def get_connection_pool_status(self) -> Dict[str, Any]:
        """Get comprehensive connection pool status and statistics"""
        status = {
            "pool_stats": self.connection_pool_stats.copy(),
            "servers": {},
            "global_registries": {
                "tools": len(self.global_tool_registry),
                "resources": len(self.global_resource_registry),
                "prompts": len(self.global_prompt_registry)
            },
            "active_sessions": len(self.active_sessions),
            "resource_subscriptions": len(self.resource_subscriptions)
        }

        for server_name, connection in self.connections.items():
            status["servers"][server_name] = {
                "status": connection.status,
                "transport_type": connection.config.transport_type,
                "last_heartbeat": getattr(connection, 'last_heartbeat', None),
                "reconnect_attempts": getattr(connection, 'reconnect_attempts', 0),
                "connection_metrics": getattr(connection, 'connection_metrics', {}).copy(),
                "tools_count": len(connection.tools),
                "resources_count": len(connection.resources),
                "prompts_count": len(getattr(connection, 'prompts', [])),
                "active_progress_tokens": len(getattr(connection, 'active_progress_tokens', {})),
                "error": connection.error
            }

        return status

    async def list_resource_templates(self, server_name: str = None) -> Dict[str, List]:
        """List resource templates from servers"""
        templates = {}

        connections_to_check = []
        if server_name:
            if server_name in self.connections:
                connections_to_check = [self.connections[server_name]]
        else:
            connections_to_check = list(self.connections.values())

        for connection in connections_to_check:
            if not await connection.ensure_connected():
                continue

            try:
                templates_result = await connection.session.list_resource_templates()
                if hasattr(templates_result, 'resourceTemplates'):
                    templates[connection.config.name] = [
                        {
                            "uri_template": template.uriTemplate,
                            "name": getattr(template, 'name', ''),
                            "description": getattr(template, 'description', ''),
                            "annotations": getattr(template, 'annotations', {})
                        }
                        for template in templates_result.resourceTemplates
                    ]
            except Exception as e:
                logger.warning(f"Failed to list resource templates from {connection.config.name}: {e}")

        return templates

    async def chat_with_bedrock(self, message: str, tools_available: List[str] = None) -> str:
        """Chat with Bedrock with fixed multiple tool call handling"""
        try:
            model_id = os.getenv('BEDROCK_MODEL_ID')
            print(model_id)
            # Get current available tools
            available_tools = self.get_available_tools()
            
            system_message = "You are a helpful AI assistant with access to various tools."
            if available_tools:
                tools_info = "\n\nAvailable tools:\n"
                for tool_key, tool_data in available_tools.items():
                    tool = tool_data["tool"]
                    tools_info += f"- {tool['name']} (server: {tool_data['server']}): {tool['description']}\n"
                system_message += tools_info

            messages = [{
                "role": "user",
                "content": [{"text": message}]
            }]

            inference_config = {
                "maxTokens": 4000,
                "temperature": 0.7,
                "topP": 0.9
            }

            system = [{"text": system_message}]

            tool_config = None
            if tools_available and available_tools:
                tools = []
                for tool_key, tool_data in available_tools.items():
                    tool = tool_data["tool"]
                    
                    # Ensure proper schema format for Bedrock
                    input_schema = tool.get("input_schema", {})
                    if not input_schema:
                        input_schema = {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    
                    # Make sure the schema has the required structure
                    if "type" not in input_schema:
                        input_schema["type"] = "object"
                    if "properties" not in input_schema:
                        input_schema["properties"] = {}
                    
                    tools.append({
                        "toolSpec": {
                            "name": tool["name"],
                            "description": tool["description"] or f"Tool from server {tool_data['server']}",
                            "inputSchema": {
                                "json": input_schema
                            }
                        }
                    })
                
                tool_config = {
                    "tools": tools,
                    "toolChoice": {"auto": {}}
                }
                
                logger.info(f"Configured {len(tools)} tools for Bedrock")

            # Keep conversing until we get an end turn
            max_iterations = 10  # Prevent infinite loops
            iteration = 0
            total_tool_count = 0  # Track total tools used across all iterations
            
            while iteration < max_iterations:
                iteration += 1
                logger.info(f"Bedrock conversation iteration {iteration}")
                
                converse_params = {
                    "modelId": model_id,
                    "messages": messages,
                    "system": system,
                    "inferenceConfig": inference_config
                }

                if tool_config:
                    converse_params["toolConfig"] = tool_config

                response = self.bedrock_client.converse(**converse_params)
                stop_reason = response.get('stopReason')

                # Capture usage and metrics for observability
                usage = response.get('usage', {})
                metrics = response.get('metrics', {})

                logger.info(f"Stop reason: {stop_reason}")
                if usage:
                    logger.info(f"Token usage - Input: {usage.get('inputTokens', 0)}, Output: {usage.get('outputTokens', 0)}")
                if metrics:
                    logger.info(f"Latency: {metrics.get('latencyMs', 0)}ms")

                # Record telemetry for this turn
                iteration_tool_count = 0

                # Handle tool use response with parallel execution
                if stop_reason == 'tool_use':
                    assistant_message = response.get('output', {}).get('message', {})
                    messages.append(assistant_message)

                    # Collect all tool use requests for parallel execution
                    tool_requests = []
                    for content in assistant_message.get('content', []):
                        if content.get('toolUse'):
                            tool_use = content['toolUse']
                            tool_name = tool_use.get('name')
                            tool_input = tool_use.get('input', {})
                            tool_use_id = tool_use.get('toolUseId')

                            logger.info(f"Tool use requested: {tool_name} with input: {tool_input}")

                            # Find the server for this tool
                            server_name = None
                            for tool_key, tool_data in available_tools.items():
                                if tool_data["tool"]["name"] == tool_name:
                                    server_name = tool_data["server"]
                                    break

                            tool_requests.append({
                                "tool_use_id": tool_use_id,
                                "tool_name": tool_name,
                                "tool_input": tool_input,
                                "server_name": server_name
                            })

                    # Execute all tools concurrently with asyncio.gather
                    logger.info(f"Executing {len(tool_requests)} tools in parallel")
                    iteration_tool_count = len(tool_requests)
                    total_tool_count += iteration_tool_count

                    # Record start time for tool execution telemetry
                    import time
                    tool_start_time = time.time() * 1000  # Convert to milliseconds

                    async def execute_single_tool(request):
                        """Execute a single tool and return formatted result as schema-compliant toolResult payload."""
                        tool_use_id = request["tool_use_id"]
                        tool_name = request["tool_name"]
                        tool_input = request["tool_input"]
                        server_name = request["server_name"]

                        if server_name:
                            try:
                                result = await self.call_tool(server_name, tool_name, tool_input)
                                # Build a single json content for toolResult with success + result/error
                                if result["success"]:
                                    result_data = result.get("result", "")
                                    # Try to return structured JSON if possible, else text via json wrapper
                                    try:
                                        if isinstance(result_data, (dict, list)):
                                            payload = {"success": True, "result": result_data}
                                        elif isinstance(result_data, str):
                                            try:
                                                parsed = json.loads(result_data)
                                                payload = {"success": True, "result": parsed}
                                            except (json.JSONDecodeError, ValueError):
                                                payload = {"success": True, "result": result_data}
                                        else:
                                            payload = {"success": True, "result": str(result_data)}
                                    except Exception:
                                        payload = {"success": True, "result": str(result_data)}

                                    return {
                                        "toolUseId": tool_use_id,
                                        "content": [{"json": payload}]
                                    }
                                else:
                                    payload = {"success": False, "error": result.get("error", "Unknown error")}
                                    return {
                                        "toolUseId": tool_use_id,
                                        "content": [{"json": payload}]
                                    }
                            except Exception as e:
                                return {
                                    "toolUseId": tool_use_id,
                                    "content": [{"json": {"success": False, "error": f"Execution error: {str(e)}"}}]
                                }
                        else:
                            return {
                                "toolUseId": tool_use_id,
                                "content": [{"json": {"success": False, "error": f"Tool {tool_name} not found"}}]
                            }

                    # Run all tools concurrently
                    tool_results = await asyncio.gather(*[
                        execute_single_tool(request) for request in tool_requests
                    ], return_exceptions=True)

                    # Handle any exceptions from gather
                    processed_results = []
                    for i, result in enumerate(tool_results):
                        if isinstance(result, Exception):
                            logger.error(f"Tool execution exception: {result}")
                            # Preserve as text content in follow-up message; no 'status' sibling (schema compliance)
                            processed_results.append({
                                "toolUseId": tool_requests[i]["tool_use_id"],
                                "content": [{"text": f"Execution exception: {str(result)}"}]
                            })
                        else:
                            processed_results.append(result)

                    tool_results = processed_results

                    # Record tool execution time
                    tool_end_time = time.time() * 1000
                    tool_execution_time = tool_end_time - tool_start_time
                    telemetry.record_tool_execution_time(tool_execution_time)
                    logger.info(f"Tool execution completed in {tool_execution_time:.2f}ms")

                    # Build toolResult message only with allowed fields
                    if tool_results:
                        tool_result_message = {"role": "user", "content": []}
                        for tool_result in tool_results:
                            tool_result_message["content"].append({
                                "toolResult": {
                                    "toolUseId": tool_result["toolUseId"],
                                    "content": tool_result["content"]
                                }
                            })
                        messages.append(tool_result_message)
                        continue
                    
                elif stop_reason in ['end_turn', 'stop_sequence', 'max_tokens']:
                    # This is the final response - capture final metrics
                    final_usage = response.get('usage', {})
                    final_metrics = response.get('metrics', {})

                    # Record final telemetry
                    telemetry.record_conversation_turn(final_usage, final_metrics, total_tool_count)

                    logger.info(f"Conversation completed. Final usage: {final_usage}, Final metrics: {final_metrics}")

                    output_message = response.get('output', {}).get('message', {})
                    for content in output_message.get('content', []):
                        if content.get('text'):
                            # Filter out thinking content before returning
                            filtered_text = self._filter_thinking_content(content['text'])
                            return {
                                "response": filtered_text,
                                "tools_used": total_tool_count,
                                "status": "success"
                            }

                    # If no text content found, return a default message
                    return {
                        "response": 'Response completed.',
                        "tools_used": total_tool_count,
                        "status": "success"
                    }

                else:
                    # Unexpected stop reason, handle as final response
                    logger.warning(f"Unexpected stop reason: {stop_reason}")
                    unexpected_usage = response.get('usage', {})
                    unexpected_metrics = response.get('metrics', {})
                    logger.info(f"Unexpected termination usage: {unexpected_usage}, metrics: {unexpected_metrics}")

                    output_message = response.get('output', {}).get('message', {})
                    for content in output_message.get('content', []):
                        if content.get('text'):
                            # Filter out thinking content before returning
                            filtered_text = self._filter_thinking_content(content['text'])
                            return {
                                "response": filtered_text,
                                "tools_used": total_tool_count,
                                "status": "success"
                            }
                    return {
                        "response": f'Response completed with stop reason: {stop_reason}',
                        "tools_used": total_tool_count,
                        "status": "success"
                    }

            # If we hit max iterations
            return {
                "response": 'Response completed after maximum iterations.',
                "tools_used": total_tool_count,
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error in Bedrock chat: {e}")
            return {
                "response": f"Error: {str(e)}",
                "tools_used": total_tool_count if 'total_tool_count' in locals() else 0,
                "status": "error"
            }

    def _filter_thinking_content(self, text: str) -> str:
        """
        Filter out thinking content from model responses.
        Removes content between <thinking> and </thinking> tags and other reasoning patterns.
        """
        import re

        if not text:
            return text

        # Remove thinking tags and their content
        # This pattern matches <thinking>...</thinking> including multiline content
        thinking_pattern = r'<thinking>.*?</thinking>'
        filtered_text = re.sub(thinking_pattern, '', text, flags=re.DOTALL | re.IGNORECASE)

        # Also remove other common reasoning patterns that might leak through
        # Remove content between <reasoning> and </reasoning> tags
        reasoning_pattern = r'<reasoning>.*?</reasoning>'
        filtered_text = re.sub(reasoning_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Remove content between <analysis> and </analysis> tags
        analysis_pattern = r'<analysis>.*?</analysis>'
        filtered_text = re.sub(analysis_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Clean up any extra whitespace that might be left
        filtered_text = re.sub(r'\n\s*\n\s*\n', '\n\n', filtered_text)  # Replace multiple newlines with double newlines
        filtered_text = filtered_text.strip()

        return filtered_text

    async def cleanup(self):
        """Enhanced cleanup with comprehensive resource management"""
        cleanup_start = time.time()
        logger.info("Starting cleanup of all connections")

        # Create a list to avoid modifying dict during iteration
        connections_to_cleanup = list(self.connections.items())

        # Cleanup each connection with enhanced error handling
        cleanup_results = []
        for name, connection in connections_to_cleanup:
            try:
                logger.info(f"Cleaning up connection: {name}")
                await connection.disconnect()
                cleanup_results.append({"server": name, "success": True})
            except Exception as e:
                logger.error(f"Error cleaning up connection {name}: {e}")
                cleanup_results.append({"server": name, "success": False, "error": str(e)})

        # Clear all registries and state
        self.connections.clear()
        self.global_tool_registry.clear()
        self.global_resource_registry.clear()
        self.global_prompt_registry.clear()
        self.resource_subscriptions.clear()
        self.active_sessions.clear()

        # Close async Bedrock client if exists
        if hasattr(self, 'async_bedrock_client') and self.async_bedrock_client:
            try:
                await self.async_bedrock_client.__aexit__(None, None, None)
            except Exception as e:
                logger.warning(f"Error closing async Bedrock client: {e}")

        cleanup_time = (time.time() - cleanup_start) * 1000
        successful_cleanups = sum(1 for result in cleanup_results if result["success"])

        logger.info(f"Enhanced cleanup completed in {cleanup_time:.2f}ms")
        logger.info(f"Successfully cleaned up {successful_cleanups}/{len(cleanup_results)} connections")

        return {
            "cleanup_time": cleanup_time,
            "total_connections": len(cleanup_results),
            "successful_cleanups": successful_cleanups,
            "cleanup_results": cleanup_results
        }


# Helper function to build UV tool executable paths
def get_uv_tool_executable_path(tool_name: str) -> str:
    """Get the full path to a UV tool executable"""
    uv_tools_dir = os.path.expanduser("~\\AppData\\Roaming\\uv\\tools")
    return os.path.join(uv_tools_dir, tool_name, "Scripts", f"awslabs.{tool_name.replace('awslabs-', '').replace('-', '-')}.exe")

# Default server configurations using uv tool run format
DEFAULT_MCP_SERVERS = [
    MCPServerConfig(
        name="cloudformation",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs.cfn-mcp-server@latest",
            "awslabs.cfn-mcp-server.exe"
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        description="AWS CloudFormation MCP Server",
        enabled=True
    ),
    MCPServerConfig(
        name="cost-explorer",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs.cost-explorer-mcp-server@latest",
            "awslabs.cost-explorer-mcp-server.exe"
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        description="AWS Cost Explorer MCP Server",
        enabled=True
    ),
    MCPServerConfig(
        name="aws-pricing",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs.aws-pricing-mcp-server@latest",
            "awslabs.aws-pricing-mcp-server.exe"
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        description="AWS Pricing MCP Server",
        enabled=True
    )
]

# Create backward compatibility aliases
MCPClientManager = EnhancedMCPClientManager
MCPServerConnection = EnhancedMCPServerConnection

# Global MCP client manager with enhanced features
mcp_manager = EnhancedMCPClientManager()

# Modified lifespan context manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan with auto-server setup"""
    # Startup
    logger.info("Starting MCP Client API")

    # Auto-configure default servers (non-blocking)
    if os.getenv("AUTO_CONFIGURE_SERVERS", "false").lower() == "true":
        logger.info("Auto-configuring default MCP servers...")

        # Use asyncio.gather with return_exceptions=True to prevent one failure from stopping others
        async def configure_server_safe(server_config):
            """Configure a server with error handling and retry logic"""
            if not server_config.enabled:
                return False, f"Server {server_config.name} is disabled"

            logger.info(f"Adding server: {server_config.name}")

            # Retry logic for timeout-prone servers
            max_retries = 2 if server_config.name in ["cost-explorer", "aws-pricing"] else 1

            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        logger.info(f"Retry attempt {attempt + 1} for {server_config.name}")
                        await asyncio.sleep(2.0)  # Wait before retry

                    success = await mcp_manager.add_server(server_config)
                    if success:
                        logger.info(f"âœ… Successfully configured {server_config.name}")
                        return True, None
                    else:
                        connection = mcp_manager.connections.get(server_config.name)
                        error_msg = connection.error if connection else "Unknown error"

                        # Don't retry if it's not a timeout error
                        if "timeout" not in error_msg.lower() or attempt == max_retries - 1:
                            logger.error(f"âŒ Failed to configure {server_config.name}: {error_msg}")
                            return False, error_msg
                        else:
                            logger.warning(f"âš ï¸  Timeout for {server_config.name}, will retry...")

                except Exception as e:
                    if attempt == max_retries - 1:
                        logger.error(f"âŒ Error configuring {server_config.name}: {e}")
                        return False, str(e)
                    else:
                        logger.warning(f"âš ï¸  Exception for {server_config.name}, will retry: {e}")

            return False, "Max retries exceeded"

        # Configure servers sequentially to reduce resource contention
        # This helps with timeout issues during concurrent startup
        results = []
        for config in DEFAULT_MCP_SERVERS:
            logger.info(f"Configuring server: {config.name}")
            result = await configure_server_safe(config)
            results.append(result)
            # Small delay between server startups
            await asyncio.sleep(1.0)

        # Count successful connections
        successful_count = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"âŒ Exception configuring {DEFAULT_MCP_SERVERS[i].name}: {result}")
            elif result[0]:  # success
                successful_count += 1

        logger.info(f"ðŸš€ Startup complete: {successful_count}/{len(DEFAULT_MCP_SERVERS)} servers connected")

        # Don't fail startup even if no servers connect
        if successful_count == 0:
            logger.warning("âš ï¸  No MCP servers connected, but API will still start")

    try:
        yield
    finally:
        # Shutdown
        logger.info("Shutting down MCP Client API")
        try:
            await mcp_manager.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Create FastAPI app
app = FastAPI(
    title="MCP Client API",
    description="Multi-server MCP client with Bedrock integration",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API Endpoints
@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "MCP Client API is running", "status": "healthy"}

@app.get("/servers")
async def list_servers():
    """List all configured MCP servers"""
    servers_info = {}
    for name, connection in mcp_manager.connections.items():
        servers_info[name] = {
            "name": name,
            "status": connection.status,
            "tools_count": len(connection.tools),
            "resources_count": len(connection.resources),
            "description": connection.config.description,
            "enabled": connection.config.enabled,
            "error": connection.error
        }
    return servers_info

@app.post("/servers")
async def add_server(config: MCPServerConfig):
    """Add a new MCP server"""
    success = await mcp_manager.add_server(config)
    if success:
        return {"message": f"Server {config.name} added successfully"}
    else:
        connection = mcp_manager.connections.get(config.name)
        error_msg = connection.error if connection else "Unknown error"
        raise HTTPException(status_code=400, detail=f"Failed to add server {config.name}: {error_msg}")

@app.get("/servers/{server_name}")
async def get_server_details(server_name: str):
    """Get detailed information about a specific server"""
    if server_name not in mcp_manager.connections:
        raise HTTPException(status_code=404, detail="Server not found")
    
    connection = mcp_manager.connections[server_name]
    return {
        "name": server_name,
        "status": connection.status,
        "config": connection.config.model_dump(),
        "tools": connection.tools,
        "resources": connection.resources,
        "error": connection.error
    }

@app.get("/tools")
async def list_tools():
    """List all available tools across all servers"""
    tools = {}
    available_tools = mcp_manager.get_available_tools()
    for tool_key, tool_data in available_tools.items():
        tools[tool_key] = {
            "server": tool_data["server"],
            "name": tool_data["tool"]["name"],
            "description": tool_data["tool"]["description"],
            "input_schema": tool_data["tool"].get("input_schema", {})
        }
    return tools

@app.post("/tools/call")
async def call_tool_endpoint(server_name: str, tool_name: str, arguments: Dict[str, Any]):
    """Call a specific tool"""
    result = await mcp_manager.call_tool(server_name, tool_name, arguments)
    if result["success"]:
        return result
    else:
        raise HTTPException(status_code=400, detail=result["error"])

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Main chat endpoint with tool integration"""
    try:
        # Get conversation ID (generate if not provided)
        conversation_id = request.conversation_id or f"conv_{uuid.uuid4().hex[:8]}"
        
        # Use tools if requested and available
        available_tools = mcp_manager.get_available_tools()
        tools_available = list(available_tools.keys()) if request.use_tools else []
        
        # Get response from Bedrock
        result = await mcp_manager.chat_with_bedrock(
            request.message,
            tools_available
        )

        # Handle both old string format and new dict format for backward compatibility
        if isinstance(result, dict):
            response_text = result.get("response", "")
            tools_used_count = result.get("tools_used", 0)
            status = result.get("status", "success")
        else:
            # Backward compatibility for string responses
            response_text = str(result)
            tools_used_count = 0
            status = "success"

        return ChatResponse(
            response=response_text,
            conversation_id=conversation_id,
            tools_used=[f"tool_{i}" for i in range(tools_used_count)],  # Create dummy tool list for count
            status=status
        )
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Additional endpoint for reconnecting to a server
@app.post("/servers/{server_name}/reconnect")
async def reconnect_server(server_name: str):
    """Reconnect to a specific server"""
    if server_name not in mcp_manager.connections:
        raise HTTPException(status_code=404, detail="Server not found")
    
    connection = mcp_manager.connections[server_name]
    
    # Disconnect first if connected
    if connection.status == "connected":
        await connection.disconnect()
    
    # Reconnect
    success = await connection.connect()
    if success:
        return {"message": f"Server {server_name} reconnected successfully"}
    else:
        raise HTTPException(status_code=500, detail=f"Failed to reconnect to server {server_name}: {connection.error}")

# Debug endpoint to check connection states
@app.get("/debug/connections")
async def debug_connections():
    """Debug endpoint to check connection states"""
    debug_info = {}
    for name, connection in mcp_manager.connections.items():
        debug_info[name] = {
            "status": connection.status,
            "error": connection.error,
            "tools_count": len(connection.tools),
            "session_exists": connection.session is not None,
            # replaced non-existent stdio_context with exit_stack presence
            "exit_stack_exists": connection.exit_stack is not None,
            "tools": [tool["name"] for tool in connection.tools]
        }
    return debug_info

# Optimization status endpoint
@app.get("/optimization-status")
async def get_optimization_status():
    """Get status of implemented optimizations"""
    return {
        "optimizations_implemented": {
            "async_exit_stack": {
                "status": "active",
                "description": "Exception-safe MCP connection management"
            },
            "parallel_tool_execution": {
                "status": "active",
                "description": "Concurrent tool execution with asyncio.gather"
            },
            "json_schema_validation": {
                "status": "active" if JSONSCHEMA_AVAILABLE else "fallback",
                "description": "Enhanced tool input validation"
            },
            "optimized_tool_registry": {
                "status": "active",
                "description": "O(1) tool lookup with global registry",
                "tools_cached": len(mcp_manager.global_tool_registry)
            },
            "telemetry_tracking": {
                "status": "active",
                "description": "Performance monitoring and metrics collection"
            },
            "enhanced_bedrock_loop": {
                "status": "active",
                "description": "Improved conversation handling with usage/metrics capture"
            }
        },
        "performance_features": {
            "connection_management": "AsyncExitStack",
            "tool_execution": "Parallel with asyncio.gather",
            "tool_lookup": "O(1) cached registry",
            "validation": "JSON Schema with fallback",
            "monitoring": "Comprehensive telemetry"
        }
    }

@app.get("/sessions/{session_id}/history")
async def get_session_history(session_id: str):
    """Get conversation history for a session."""
    return {
        "session_id": session_id,
        "message_count": 0,
        "total_tools_used": 0,
        "history": [],
        "note": "Session history requires enhanced mode. Use main_enhanced.py for full session management."
    }

@app.get("/telemetry")
async def get_telemetry():
    """Get telemetry and performance metrics"""
    return {
        "telemetry": telemetry.get_summary(),
        "server_status": {
            server_name: {
                "status": connection.status,
                "tools_count": len(connection.tool_registry),
                "error": connection.error
            }
            for server_name, connection in mcp_manager.connections.items()
        },
        "optimization_features": {
            "async_exit_stack": True,
            "parallel_tool_execution": True,
            "json_schema_validation": JSONSCHEMA_AVAILABLE,
            "global_tool_registry": len(mcp_manager.global_tool_registry),
            "telemetry_tracking": True
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", "8000")),
        reload=True,
        log_level=os.getenv("LOG_LEVEL", "info")
    )
