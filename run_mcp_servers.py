#!/usr/bin/env python3
"""
MCP Server Runner and Manager

This script helps you run and manage multiple MCP servers for development and testing.
"""

import asyncio
import json
import logging
import os
import signal
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class MCPServerManager:
    """Manages multiple MCP servers"""
    
    def __init__(self, config_file: str = "mcp_config.json"):
        self.config_file = config_file
        self.config = {}
        self.processes: Dict[str, subprocess.Popen] = {}
        self.running = False
        
    def load_config(self):
        """Load MCP server configuration"""
        try:
            with open(self.config_file, 'r') as f:
                self.config = json.load(f)
            logger.info(f"Loaded configuration from {self.config_file}")
        except FileNotFoundError:
            logger.error(f"Configuration file {self.config_file} not found")
            sys.exit(1)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            sys.exit(1)
    
    def start_server(self, server_name: str, server_config: Dict) -> bool:
        """Start a single MCP server"""
        try:
            command = [server_config["command"]] + server_config.get("args", [])
            env = os.environ.copy()
            env.update(server_config.get("env", {}))
            
            logger.info(f"Starting server {server_name}: {' '.join(command)}")
            
            process = subprocess.Popen(
                command,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.processes[server_name] = process
            logger.info(f"Server {server_name} started with PID {process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start server {server_name}: {e}")
            return False
    
    def stop_server(self, server_name: str) -> bool:
        """Stop a single MCP server"""
        if server_name not in self.processes:
            logger.warning(f"Server {server_name} is not running")
            return False
        
        try:
            process = self.processes[server_name]
            process.terminate()
            
            # Wait for graceful shutdown
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                logger.warning(f"Server {server_name} did not terminate gracefully, killing")
                process.kill()
                process.wait()
            
            del self.processes[server_name]
            logger.info(f"Server {server_name} stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop server {server_name}: {e}")
            return False
    
    def start_all_servers(self, server_filter: Optional[List[str]] = None):
        """Start all configured MCP servers"""
        servers = self.config.get("mcpServers", {})
        
        if server_filter:
            servers = {k: v for k, v in servers.items() if k in server_filter}
        
        started_count = 0
        for server_name, server_config in servers.items():
            if self.start_server(server_name, server_config):
                started_count += 1
        
        logger.info(f"Started {started_count}/{len(servers)} servers")
        self.running = True
    
    def stop_all_servers(self):
        """Stop all running MCP servers"""
        stopped_count = 0
        for server_name in list(self.processes.keys()):
            if self.stop_server(server_name):
                stopped_count += 1
        
        logger.info(f"Stopped {stopped_count} servers")
        self.running = False
    
    def check_server_health(self, server_name: str) -> Dict[str, any]:
        """Check health of a specific server"""
        if server_name not in self.processes:
            return {"status": "not_running", "pid": None}
        
        process = self.processes[server_name]
        
        # Check if process is still running
        if process.poll() is None:
            return {
                "status": "running",
                "pid": process.pid,
                "returncode": None
            }
        else:
            return {
                "status": "stopped",
                "pid": process.pid,
                "returncode": process.returncode
            }
    
    def get_server_status(self) -> Dict[str, Dict]:
        """Get status of all configured servers"""
        servers = self.config.get("mcpServers", {})
        status = {}
        
        for server_name in servers.keys():
            status[server_name] = self.check_server_health(server_name)
        
        return status
    
    def monitor_servers(self):
        """Monitor server health and restart if needed"""
        while self.running:
            try:
                for server_name, process in list(self.processes.items()):
                    if process.poll() is not None:
                        logger.warning(f"Server {server_name} has stopped unexpectedly")
                        
                        # Get server config for restart
                        server_config = self.config["mcpServers"].get(server_name)
                        if server_config:
                            logger.info(f"Attempting to restart {server_name}")
                            del self.processes[server_name]
                            self.start_server(server_name, server_config)
                
                # Sleep before next check
                asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"Error in server monitoring: {e}")
                asyncio.sleep(5)
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down servers...")
            self.stop_all_servers()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="MCP Server Manager")
    parser.add_argument(
        "--config",
        default="mcp_config.json",
        help="Configuration file path"
    )
    parser.add_argument(
        "--servers",
        nargs="*",
        help="Specific servers to start (default: all)"
    )
    parser.add_argument(
        "--action",
        choices=["start", "stop", "status", "monitor"],
        default="start",
        help="Action to perform"
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create manager
    manager = MCPServerManager(args.config)
    manager.load_config()
    manager.setup_signal_handlers()
    
    try:
        if args.action == "start":
            manager.start_all_servers(args.servers)
            
            if args.servers and len(args.servers) == 1:
                # If starting a single server, wait for it to finish
                server_name = args.servers[0]
                if server_name in manager.processes:
                    process = manager.processes[server_name]
                    try:
                        process.wait()
                    except KeyboardInterrupt:
                        logger.info("Interrupted, stopping server...")
                        manager.stop_server(server_name)
            else:
                # Multiple servers or all servers - monitor them
                logger.info("Monitoring servers... Press Ctrl+C to stop")
                try:
                    while manager.running:
                        asyncio.sleep(1)
                except KeyboardInterrupt:
                    logger.info("Interrupted, stopping all servers...")
                    manager.stop_all_servers()
        
        elif args.action == "stop":
            if args.servers:
                for server_name in args.servers:
                    manager.stop_server(server_name)
            else:
                manager.stop_all_servers()
        
        elif args.action == "status":
            status = manager.get_server_status()
            print("\nServer Status:")
            print("-" * 50)
            for server_name, server_status in status.items():
                print(f"{server_name:30} {server_status['status']:15} PID: {server_status.get('pid', 'N/A')}")
        
        elif args.action == "monitor":
            manager.start_all_servers(args.servers)
            logger.info("Starting monitoring mode...")
            try:
                manager.monitor_servers()
            except KeyboardInterrupt:
                logger.info("Monitoring interrupted, stopping servers...")
                manager.stop_all_servers()
    
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
